{"jobs": [{"id": "1", "company": "Photosnap", "logo": "/assets/images/photosnap.svg", "new": true, "featured": true, "position": "Senior Frontend Developer", "role": "Frontend", "level": "Senior", "postedAt": "1d ago", "contract": "Full Time", "location": "USA", "languages": ["HTML", "CSS", "JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "tools": [], "slots": 2, "category": "Finance"}, {"id": "2", "company": "Manage", "logo": "/assets/images/manage.svg", "new": true, "featured": true, "position": "Fullstack Developer", "role": "Fullstack", "level": "Mid Level", "postedAt": "1d ago", "contract": "Part Time", "location": "Remote", "languages": ["Python"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Finance", "categoryLink": "ict", "tools": ["React"]}, {"id": "3", "company": "Account", "logo": "/assets/images/account.svg", "new": true, "featured": false, "position": "Junior Frontend Developer", "role": "Frontend", "level": "Junior", "postedAt": "2d ago", "contract": "Part Time", "location": "Remote", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Finance", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "4", "company": "MyHome", "logo": "/assets/images/myhome.svg", "new": false, "featured": false, "position": "Social Worker", "role": "Social Worker", "level": "Junior", "postedAt": "5d ago", "contract": "Contract", "location": "USA", "languages": ["CSS", "JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Social Science", "categoryLink": "ict", "tools": []}, {"id": "5", "company": "Loop Studios", "logo": "/assets/images/loop-studios.svg", "new": false, "featured": false, "position": "Software Engineer", "role": "Fullstack", "level": "Mid Level", "postedAt": "1w ago", "contract": "Full Time", "location": "Japan", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["<PERSON>", "Sass"]}, {"id": "6", "company": "FaceIt", "logo": "/assets/images/faceit.svg", "new": false, "featured": false, "position": "Junior Backend <PERSON>", "role": "Backend", "level": "Junior", "postedAt": "2w ago", "contract": "Full Time", "location": "UK", "languages": ["<PERSON>"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["RoR"]}, {"id": "7", "company": "Shortly", "logo": "/assets/images/shortly.svg", "new": false, "featured": false, "position": "Junior Developer", "role": "Frontend", "level": "Junior", "postedAt": "2w ago", "contract": "Full Time", "location": "Canada", "languages": ["HTML", "JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["Sass"]}, {"id": "8", "company": "Insure", "logo": "/assets/images/insure.svg", "new": false, "featured": false, "position": "Junior Frontend Developer", "role": "Frontend", "level": "Junior", "postedAt": "2w ago", "contract": "Full Time", "location": "USA", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["<PERSON><PERSON>", "Sass"]}, {"id": "9", "company": "Eyecam Co.", "logo": "/assets/images/eyecam-co.svg", "new": false, "featured": false, "position": "Full Stack Engineer", "role": "Fullstack", "level": "Mid Level", "postedAt": "3w ago", "contract": "Full Time", "location": "Canada", "languages": ["JavaScript", "Python"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Finance", "categoryLink": "ict", "tools": ["Django"]}, {"id": "10", "company": "The Air Filter Company", "logo": "/assets/images/the-air-filter-company.svg", "new": false, "featured": false, "position": "Front-end Dev", "role": "Frontend", "level": "Junior", "postedAt": "1mo ago", "contract": "Part Time", "location": "Canada", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Business", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "11", "company": "Creative Studio", "logo": "/assets/images/the-air-filter-company.svg", "new": false, "featured": true, "position": "Graphic Designer", "role": "UI Designer", "level": "Junior", "postedAt": "1mo ago", "contract": "Part Time", "location": "Los Angeles, CA", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Education", "categoryLink": "ict", "tools": ["Adobe Illustrator", "Adobe photoshop"]}, {"id": "12", "company": "Innovate Inc", "logo": "/assets/images/the-air-filter-company.svg", "new": false, "featured": true, "position": "Product Manager", "role": "Manager", "level": "Senior", "postedAt": "1mo ago", "contract": "Part Time", "location": "New York, NY ", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["Adobe Illustrator", "Adobe photoshop"]}, {"id": "13", "company": "Vladn Inc", "logo": "/assets/images/the-air-filter-company.svg", "new": false, "featured": true, "position": "DevOps enginner", "role": "Software Engineer", "level": "Senior", "postedAt": "1mo ago", "contract": "Part Time", "location": "New York, NY ", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["Adobe Illustrator", "Adobe photoshop"]}, {"id": "14", "company": "GYS inc.", "logo": "/assets/images/myhome.svg", "new": false, "featured": false, "position": "Business Consultant", "role": "Consultant", "level": "Senior", "postedAt": "1mo ago", "contract": "Full Time", "location": "Canada", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Business", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "15", "company": "GYS inc.", "logo": "/assets/images/myhome.svg", "new": false, "featured": false, "position": "Electrical Engineer", "role": "Electrician", "level": "Senior", "postedAt": "1mo ago", "contract": "Full Time", "location": "Ethiopia", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Education", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "16", "company": "EastSide", "logo": "/assets/images/faceit.svg", "new": false, "featured": false, "position": "Electrical Engineer", "role": "Electrician", "level": "Senior", "postedAt": "1mo ago", "contract": "Full Time", "location": "Ethiopia", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$110,000 - $140,000", "slots": 2, "category": "Engineering", "categoryLink": "ict", "tools": ["React", "Sass"]}], "sectors": [{"title": "ICT", "icon": "/assets/images/ict.svg", "link": "ict", "count": 2, "id": "863c"}, {"title": "Business", "icon": "/assets/images/business.svg", "link": "business", "count": 12, "id": "7af6"}, {"title": "Education", "icon": "/assets/images/education.svg", "link": "education", "count": 32, "id": "8719"}, {"title": "Engineering", "icon": "/assets/images/enginnering.svg", "link": "engineering", "count": 22, "id": "55e9"}, {"title": "Finance", "icon": "/assets/images/finance.svg", "link": "finance", "count": 98, "id": "6b6d"}, {"title": "Science", "icon": "/assets/images/science.svg", "link": "science", "count": 3, "id": "bebd"}, {"title": "Social Science", "icon": "/assets/images/social-science.svg", "link": "social-science", "count": 48, "id": "87ce"}], "users": [{"id": "7b90", "firstName": "<PERSON><PERSON>", "lastName": "Verhof", "email": "<EMAIL>", "password": "$2b$10$HSUszP3yDntaLF6V9ds1muEd7KejnxsGWSv8rPZEg3TOP82pTNodS"}], "applications": [{"id": "1", "jobId": "1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "coverLetter": "I am excited to apply!", "resume": "resume.pdf"}]}