<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1500 500">
  <defs>
    <style>
      .cls-1 {
        fill: url(#Silver_04-2);
      }

      .cls-2 {
        fill: #955738;
      }

      .cls-3 {
        fill: #636464;
      }

      .cls-4 {
        fill: url(#linear-gradient-2);
      }

      .cls-5 {
        fill: #e3f4fc;
      }

      .cls-6 {
        fill: #172833;
      }

      .cls-7 {
        fill: #232f38;
      }

      .cls-7, .cls-8, .cls-9, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16, .cls-17, .cls-18, .cls-19, .cls-20, .cls-21, .cls-22, .cls-23, .cls-24, .cls-25, .cls-26, .cls-27, .cls-28 {
        isolation: isolate;
      }

      .cls-7, .cls-26 {
        opacity: .1;
      }

      .cls-29 {
        fill: url(#linear-gradient-17);
      }

      .cls-30 {
        fill: #f89c19;
      }

      .cls-31 {
        fill: #323b42;
      }

      .cls-32 {
        fill: #e1e7e6;
      }

      .cls-33 {
        fill: url(#linear-gradient-52);
      }

      .cls-33, .cls-34, .cls-35, .cls-36 {
        mix-blend-mode: multiply;
      }

      .cls-37 {
        fill: url(#linear-gradient-43);
      }

      .cls-34 {
        fill: url(#linear-gradient-51);
      }

      .cls-38 {
        fill: url(#Silver_04-8);
      }

      .cls-39 {
        fill: #d7ecf4;
      }

      .cls-8 {
        fill: #231f20;
        opacity: .27;
      }

      .cls-40 {
        fill: url(#Gold_12-8);
      }

      .cls-9 {
        opacity: .16;
      }

      .cls-9, .cls-13 {
        fill: #425159;
      }

      .cls-10 {
        fill: #11100f;
        opacity: .33;
      }

      .cls-11 {
        opacity: .29;
      }

      .cls-11, .cls-21, .cls-22 {
        fill: #774230;
      }

      .cls-41 {
        fill: #151213;
      }

      .cls-42 {
        fill: url(#linear-gradient-15);
      }

      .cls-43 {
        fill: #3f3e3d;
      }

      .cls-35 {
        fill: url(#linear-gradient-50);
      }

      .cls-44 {
        fill: url(#linear-gradient-18);
      }

      .cls-45 {
        fill: #955437;
      }

      .cls-46 {
        fill: url(#linear-gradient-40);
      }

      .cls-47 {
        fill: url(#linear-gradient-28);
      }

      .cls-48 {
        fill: url(#linear-gradient-14);
      }

      .cls-49 {
        fill: url(#linear-gradient-36);
      }

      .cls-50 {
        fill: url(#linear-gradient-20);
      }

      .cls-51 {
        fill: url(#linear-gradient-6);
      }

      .cls-52, .cls-15, .cls-23, .cls-27 {
        fill: #383736;
      }

      .cls-53 {
        fill: #303030;
      }

      .cls-54 {
        fill: url(#Gold_12-3);
      }

      .cls-55 {
        fill: url(#Gold_12-5);
      }

      .cls-56 {
        fill: #544e50;
      }

      .cls-13 {
        opacity: .2;
      }

      .cls-57 {
        fill: url(#linear-gradient-35);
      }

      .cls-58 {
        fill: url(#linear-gradient-48);
      }

      .cls-59 {
        fill: url(#linear-gradient-32);
      }

      .cls-60 {
        fill: #cde6f4;
      }

      .cls-61 {
        fill: url(#Silver_04);
      }

      .cls-62 {
        fill: url(#linear-gradient-11);
      }

      .cls-63 {
        fill: #009688;
      }

      .cls-64 {
        fill: url(#linear-gradient-39);
      }

      .cls-36 {
        fill: url(#linear-gradient-53);
      }

      .cls-65 {
        fill: url(#Silver_04-6);
      }

      .cls-14 {
        fill: #16212a;
      }

      .cls-14, .cls-25 {
        opacity: .25;
      }

      .cls-15 {
        opacity: .19;
      }

      .cls-66 {
        fill: url(#linear-gradient-10);
      }

      .cls-67 {
        fill: #a1633f;
      }

      .cls-68 {
        fill: #955637;
      }

      .cls-69 {
        fill: url(#radial-gradient);
      }

      .cls-70 {
        fill: url(#linear-gradient-49);
      }

      .cls-71 {
        fill: url(#linear-gradient-37);
      }

      .cls-72 {
        fill: #444f60;
      }

      .cls-73 {
        fill: #a55d41;
      }

      .cls-74 {
        fill: url(#Silver_04-3);
      }

      .cls-75 {
        fill: url(#linear-gradient-16);
      }

      .cls-76 {
        fill: url(#linear-gradient-31);
      }

      .cls-77 {
        fill: url(#Silver_04-7);
      }

      .cls-78 {
        fill: url(#Gold_12-6);
      }

      .cls-79 {
        fill: #b76e48;
      }

      .cls-80 {
        fill: url(#linear-gradient-41);
      }

      .cls-81 {
        fill: url(#Gold_12);
      }

      .cls-82 {
        fill: #573731;
      }

      .cls-83 {
        fill: #2b2a29;
      }

      .cls-16 {
        fill: #c69d8a;
      }

      .cls-16, .cls-21, .cls-24, .cls-28 {
        opacity: .35;
      }

      .cls-17 {
        fill: #d8ae97;
        opacity: .34;
      }

      .cls-84 {
        fill: #a1623e;
      }

      .cls-85 {
        fill: url(#linear-gradient-21);
      }

      .cls-86 {
        fill: #a2623e;
      }

      .cls-18 {
        opacity: .05;
      }

      .cls-18, .cls-20 {
        fill: #020202;
      }

      .cls-19 {
        opacity: .39;
      }

      .cls-19, .cls-25 {
        fill: #16202a;
      }

      .cls-20 {
        opacity: .02;
      }

      .cls-87 {
        fill: url(#linear-gradient-25);
      }

      .cls-88 {
        fill: #af7152;
      }

      .cls-89 {
        fill: #454d60;
      }

      .cls-90 {
        fill: url(#linear-gradient-5);
      }

      .cls-91 {
        fill: url(#linear-gradient-54);
      }

      .cls-92 {
        fill: url(#linear-gradient-45);
      }

      .cls-22 {
        opacity: .21;
      }

      .cls-93 {
        fill: #009789;
      }

      .cls-94 {
        fill: url(#linear-gradient-55);
      }

      .cls-23 {
        opacity: .41;
      }

      .cls-95 {
        fill: url(#linear-gradient-34);
      }

      .cls-96 {
        fill: url(#Silver_04-10);
      }

      .cls-24 {
        fill: #98b0b6;
      }

      .cls-97 {
        fill: #004b44;
      }

      .cls-98 {
        fill: url(#linear-gradient-7);
      }

      .cls-99 {
        fill: #515151;
      }

      .cls-100 {
        fill: url(#Silver_04-4);
      }

      .cls-27 {
        opacity: .3;
      }

      .cls-101 {
        fill: #151514;
      }

      .cls-102 {
        fill: url(#linear-gradient-24);
      }

      .cls-103 {
        fill: url(#Silver_04-9);
      }

      .cls-104 {
        fill: #221f1f;
      }

      .cls-105 {
        fill: url(#linear-gradient-9);
      }

      .cls-106 {
        fill: url(#linear-gradient-22);
      }

      .cls-107 {
        fill: url(#linear-gradient-42);
      }

      .cls-108 {
        fill: url(#linear-gradient-3);
      }

      .cls-28 {
        fill: #97afb6;
      }

      .cls-109 {
        fill: #007c6e;
      }

      .cls-110 {
        fill: url(#linear-gradient);
      }

      .cls-111 {
        fill: #e8edef;
      }

      .cls-112 {
        fill: url(#linear-gradient-27);
      }

      .cls-113 {
        fill: url(#Silver_04-5);
      }

      .cls-114 {
        fill: url(#linear-gradient-33);
      }

      .cls-115 {
        fill: url(#linear-gradient-38);
      }

      .cls-116 {
        fill: url(#linear-gradient-29);
      }

      .cls-117 {
        fill: url(#linear-gradient-30);
      }

      .cls-118 {
        fill: url(#Gold_12-2);
      }

      .cls-119 {
        fill: #965437;
      }

      .cls-120 {
        fill: #1e1e1e;
      }

      .cls-121 {
        fill: url(#Gold_12-7);
      }

      .cls-122 {
        fill: url(#linear-gradient-23);
      }

      .cls-123 {
        fill: url(#linear-gradient-19);
      }

      .cls-124 {
        fill: url(#linear-gradient-4);
      }

      .cls-125 {
        fill: #141011;
      }

      .cls-126 {
        fill: url(#Gold_12-4);
      }

      .cls-127 {
        fill: #2a3244;
      }

      .cls-128 {
        fill: url(#linear-gradient-8);
      }

      .cls-129 {
        fill: #1b415b;
      }

      .cls-130 {
        fill: url(#linear-gradient-44);
      }

      .cls-131 {
        fill: url(#linear-gradient-47);
      }

      .cls-132 {
        fill: url(#linear-gradient-46);
      }

      .cls-133 {
        fill: url(#linear-gradient-12);
      }

      .cls-134 {
        fill: url(#linear-gradient-13);
      }

      .cls-135 {
        fill: url(#linear-gradient-26);
      }

      .cls-136 {
        fill: #b6d0ce;
      }
    </style>
    <linearGradient id="linear-gradient" x1="277.29" y1="18.95" x2="277.29" y2="182.62" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009789"/>
      <stop offset=".18" stop-color="#019386"/>
      <stop offset=".37" stop-color="#03877c"/>
      <stop offset=".55" stop-color="#07736c"/>
      <stop offset=".73" stop-color="#0c5756"/>
      <stop offset=".92" stop-color="#133339"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="257.4" y1="162.8" x2="321.06" y2="162.8" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009789"/>
      <stop offset=".43" stop-color="#009588"/>
      <stop offset=".59" stop-color="#028e82"/>
      <stop offset=".7" stop-color="#048379"/>
      <stop offset=".79" stop-color="#07726b"/>
      <stop offset=".87" stop-color="#0b5c5a"/>
      <stop offset=".94" stop-color="#104144"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="277.3" y1="18.95" x2="277.3" y2="182.67" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009789"/>
      <stop offset=".18" stop-color="#019386"/>
      <stop offset=".37" stop-color="#03877c"/>
      <stop offset=".55" stop-color="#07736c"/>
      <stop offset=".73" stop-color="#0c5756"/>
      <stop offset=".92" stop-color="#133339"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="257.4" y1="162.81" x2="321.06" y2="162.81" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009789"/>
      <stop offset=".43" stop-color="#009588"/>
      <stop offset=".59" stop-color="#028e82"/>
      <stop offset=".7" stop-color="#048379"/>
      <stop offset=".79" stop-color="#07726b"/>
      <stop offset=".87" stop-color="#0b5c5a"/>
      <stop offset=".94" stop-color="#104144"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="224.28" y1="182.26" x2="271.2" y2="182.26" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009789"/>
      <stop offset=".43" stop-color="#009588"/>
      <stop offset=".59" stop-color="#028e82"/>
      <stop offset=".7" stop-color="#048379"/>
      <stop offset=".79" stop-color="#07726b"/>
      <stop offset=".87" stop-color="#0b5c5a"/>
      <stop offset=".94" stop-color="#104144"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="234.13" y1="166.06" x2="257.52" y2="199.57" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009789"/>
      <stop offset=".43" stop-color="#009588"/>
      <stop offset=".59" stop-color="#028e82"/>
      <stop offset=".7" stop-color="#048379"/>
      <stop offset=".79" stop-color="#07726b"/>
      <stop offset=".87" stop-color="#0b5c5a"/>
      <stop offset=".94" stop-color="#104144"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="268.82" y1="217.03" x2="268.82" y2="209.33" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f89c1a"/>
      <stop offset="0" stop-color="#f89c1a"/>
      <stop offset="1" stop-color="#f2521d"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="733.78" y1="120.1" x2="780.72" y2="120.1" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="754.39" y1="144.73" x2="760.11" y2="144.73" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="738.43" y1="179.04" x2="776.08" y2="179.04" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="735.97" y1="213.15" x2="778.53" y2="213.15" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="405.95" y1="119.44" x2="456.7" y2="119.44" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="426.69" y1="145.91" x2="432.96" y2="145.91" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="409.36" y1="183.1" x2="450.29" y2="183.1" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="406.77" y1="220.06" x2="452.88" y2="220.06" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="1235.4" y1="165.45" x2="1243.4" y2="217.82" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#714e9f"/>
      <stop offset="1" stop-color="#1a1838"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="1238.83" y1="161.62" x2="1239.62" y2="166.76" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#714e9f"/>
      <stop offset="1" stop-color="#1a1838"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="1261.15" y1="51.77" x2="1261.15" y2="23.1" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="1265.83" y1="40.78" x2="1258.11" y2="40.78" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="1260.59" y1="36.81" x2="1291.45" y2="8.86" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="1280.84" y1="28.27" x2="1263.36" y2="28.27" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="1249.3" y1="74.13" x2="1249.3" y2="37.11" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="1256.19" y1="59.72" x2="1244.85" y2="59.72" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="1244.13" y1="98.34" x2="1244.13" y2="69.67" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="1248.86" y1="87.35" x2="1241.14" y2="87.35" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-26" x1="1234.66" y1="95.53" x2="1234.66" y2="66.84" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-27" x1="1239.37" y1="84.54" x2="1231.66" y2="84.54" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-28" x1="1224.27" y1="85.41" x2="1217.35" y2="57.57" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-29" x1="1228.68" y1="74.54" x2="1216.14" y2="74.54" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-30" x1="1226.17" y1="108.03" x2="1219.25" y2="80.19" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="1230.63" y1="97.22" x2="1218.08" y2="97.22" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-32" x1="1225.15" y1="125.26" x2="1215.6" y2="86.56" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="1232.32" y1="110.25" x2="1213.5" y2="110.25" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-34" x1="1232.12" y1="152.67" x2="1225.21" y2="124.83" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-35" x1="1237.75" y1="150.77" x2="1263.03" y2="137.2" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="1224.28" y1="62.94" x2="1240.35" y2="39.18" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-37" x1="1234.83" y1="54.2" x2="1227.27" y2="54.2" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-38" x1="1241.08" y1="119.74" x2="1241.08" y2="99.26" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="1244.44" y1="111.99" x2="1238.97" y2="111.99" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="1237.79" y1="142.32" x2="1237.79" y2="121.84" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-41" x1="1244.05" y1="144.05" x2="1247.79" y2="123.95" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="1249.56" y1="119.46" x2="1260.66" y2="89.76" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-43" x1="1264.89" y1="99.12" x2="1246.79" y2="99.12" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-44" x1="1251.65" y1="93.66" x2="1262.11" y2="76.09" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-45" x1="1265.28" y1="79.89" x2="1252.73" y2="79.89" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-46" x1="1264.03" y1="68.12" x2="1276.39" y2="47.47" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-47" x1="1278" y1="56.59" x2="1261.51" y2="56.59" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009493"/>
      <stop offset=".19" stop-color="#01908f"/>
      <stop offset=".37" stop-color="#038484"/>
      <stop offset=".56" stop-color="#077072"/>
      <stop offset=".74" stop-color="#0c5459"/>
      <stop offset=".93" stop-color="#133038"/>
      <stop offset="1" stop-color="#16202a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-48" x1="533.06" y1="196.47" x2="622.22" y2="107.31" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <linearGradient id="linear-gradient-49" x1="875.64" y1="196.47" x2="964.8" y2="107.31" gradientTransform="translate(0 214)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#009688"/>
      <stop offset="1" stop-color="#0b6468"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="744.68" cy="56.62" fx="744.68" fy="56.62" r="45.35" gradientTransform="translate(-8.3 2.32) scale(1.02)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#097d83"/>
      <stop offset=".19" stop-color="#08797e" stop-opacity=".98"/>
      <stop offset=".43" stop-color="#086d73" stop-opacity=".93"/>
      <stop offset=".69" stop-color="#065b5f" stop-opacity=".84"/>
      <stop offset=".97" stop-color="#054144" stop-opacity=".71"/>
      <stop offset="1" stop-color="#053e41" stop-opacity=".7"/>
    </radialGradient>
    <linearGradient id="linear-gradient-50" x1="935.84" y1="284.41" x2="935.84" y2="267.62" gradientTransform="translate(445.27 999.57) rotate(-90)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset=".2" stop-color="#fbfbfb"/>
      <stop offset=".38" stop-color="#f0f1f1"/>
      <stop offset=".54" stop-color="#dedfe0"/>
      <stop offset=".7" stop-color="#c5c6c7"/>
      <stop offset=".86" stop-color="#a5a6a8"/>
      <stop offset="1" stop-color="#808285"/>
    </linearGradient>
    <linearGradient id="linear-gradient-51" y1="649.18" y2="632.39" gradientTransform="translate(1417.3 999.57) rotate(-90) scale(1 -1)" xlink:href="#linear-gradient-50"/>
    <linearGradient id="linear-gradient-52" x1="1476.72" y1="-66.09" x2="1476.72" y2="-81.8" gradientTransform="translate(2225.61 30) rotate(-180)" xlink:href="#linear-gradient-50"/>
    <linearGradient id="linear-gradient-53" x1="1474.96" y1="970.87" x2="1474.96" y2="955.16" gradientTransform="translate(2225.61 -942.03) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-50"/>
    <linearGradient id="Silver_04" data-name="Silver 04" x1="885.44" y1="304.74" x2="884.24" y2="304.74" gradientTransform="translate(445.27 999.57) rotate(-90)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cbd1d6"/>
      <stop offset=".09" stop-color="#c7cdd2"/>
      <stop offset=".17" stop-color="#bcc2c7"/>
      <stop offset=".25" stop-color="#aab1b5"/>
      <stop offset=".25" stop-color="#aab1b5"/>
      <stop offset=".25" stop-color="#aab1b5"/>
      <stop offset=".29" stop-color="#cad1d4"/>
      <stop offset=".33" stop-color="#e1e8eb"/>
      <stop offset=".37" stop-color="#eff6f9"/>
      <stop offset=".4" stop-color="#f4fbfe"/>
      <stop offset=".63" stop-color="#f4fbfe"/>
      <stop offset=".75" stop-color="#979da1"/>
      <stop offset=".83" stop-color="#b0b7bc"/>
      <stop offset=".85" stop-color="#949a9f"/>
      <stop offset=".87" stop-color="#7d8286"/>
      <stop offset=".89" stop-color="#6e7478"/>
      <stop offset=".91" stop-color="#6a6f73"/>
      <stop offset=".93" stop-color="#70757a"/>
      <stop offset=".95" stop-color="#84898d"/>
      <stop offset=".97" stop-color="#a3a9ad"/>
      <stop offset="1" stop-color="#cbd1d6"/>
    </linearGradient>
    <linearGradient id="Silver_04-2" data-name="Silver 04" x1="938.6" y1="348.03" x2="938.6" y2="349.37" xlink:href="#Silver_04"/>
    <linearGradient id="Silver_04-3" data-name="Silver 04" x1="991.75" y1="304.73" x2="992.97" y2="304.73" xlink:href="#Silver_04"/>
    <linearGradient id="Silver_04-4" data-name="Silver 04" x1="938.6" y1="261.45" x2="938.6" y2="260.08" xlink:href="#Silver_04"/>
    <linearGradient id="Gold_12" data-name="Gold 12" x1="990.55" y1="304.73" x2="991.77" y2="304.73" gradientTransform="translate(445.27 999.57) rotate(-90)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ffedc2"/>
      <stop offset=".18" stop-color="#f7efa6"/>
      <stop offset=".38" stop-color="#f3d16e"/>
      <stop offset=".47" stop-color="#eec65e"/>
      <stop offset=".57" stop-color="#e4b650"/>
      <stop offset=".62" stop-color="#af7121"/>
      <stop offset=".65" stop-color="#af7121"/>
      <stop offset=".72" stop-color="#f2d76c"/>
      <stop offset=".79" stop-color="#f8e77d"/>
      <stop offset=".84" stop-color="#f7f1a4"/>
      <stop offset=".89" stop-color="#fff2c5"/>
      <stop offset=".94" stop-color="#e6ad06"/>
      <stop offset=".94" stop-color="#e6ad06"/>
      <stop offset="1" stop-color="#fbcb4a"/>
    </linearGradient>
    <linearGradient id="Gold_12-2" data-name="Gold 12" x1="938.6" y1="262.79" x2="938.6" y2="261.43" xlink:href="#Gold_12"/>
    <linearGradient id="Gold_12-3" data-name="Gold 12" x1="938.6" y1="346.68" x2="938.6" y2="348.03" xlink:href="#Gold_12"/>
    <linearGradient id="Gold_12-4" data-name="Gold 12" x1="886.64" y1="304.74" x2="885.44" y2="304.74" xlink:href="#Gold_12"/>
    <linearGradient id="Silver_04-5" data-name="Silver 04" x1="938.61" y1="339.96" x2="938.61" y2="346.69" xlink:href="#Silver_04"/>
    <linearGradient id="Silver_04-6" data-name="Silver 04" x1="892.63" x2="886.64" xlink:href="#Silver_04"/>
    <linearGradient id="Silver_04-7" data-name="Silver 04" x1="938.6" y1="269.52" x2="938.6" y2="262.77" xlink:href="#Silver_04"/>
    <linearGradient id="Silver_04-8" data-name="Silver 04" x1="984.56" y1="304.73" x2="990.57" y2="304.73" xlink:href="#Silver_04"/>
    <linearGradient id="Gold_12-5" data-name="Gold 12" x1="938.61" y1="339.15" x2="938.61" y2="339.97" xlink:href="#Gold_12"/>
    <linearGradient id="Gold_12-6" data-name="Gold 12" x1="893.35" y1="304.74" x2="892.63" y2="304.74" xlink:href="#Gold_12"/>
    <linearGradient id="Gold_12-7" data-name="Gold 12" x1="938.6" y1="270.32" x2="938.6" y2="269.49" xlink:href="#Gold_12"/>
    <linearGradient id="Gold_12-8" data-name="Gold 12" x1="983.85" y1="304.73" x2="984.58" y2="304.73" xlink:href="#Gold_12"/>
    <linearGradient id="linear-gradient-54" x1="982.05" y1="304.72" x2="983.86" y2="304.72" gradientTransform="translate(445.27 999.57) rotate(-90)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cbd1d6"/>
      <stop offset=".09" stop-color="#c7cdd2"/>
      <stop offset=".17" stop-color="#bcc2c7"/>
      <stop offset=".25" stop-color="#aab1b5"/>
      <stop offset=".25" stop-color="#aab1b5"/>
      <stop offset=".25" stop-color="#aab1b5"/>
      <stop offset=".29" stop-color="#cad1d4"/>
      <stop offset=".33" stop-color="#e1e8eb"/>
      <stop offset=".37" stop-color="#eff6f9"/>
      <stop offset=".4" stop-color="#f4fbfe"/>
      <stop offset=".63" stop-color="#f4fbfe"/>
      <stop offset=".75" stop-color="#979da1"/>
      <stop offset=".83" stop-color="#b0b7bc"/>
      <stop offset=".85" stop-color="#949a9f"/>
      <stop offset=".87" stop-color="#7d8286"/>
      <stop offset=".89" stop-color="#6e7478"/>
      <stop offset=".91" stop-color="#6a6f73"/>
      <stop offset=".93" stop-color="#70757a"/>
      <stop offset=".95" stop-color="#84898d"/>
      <stop offset=".97" stop-color="#a3a9ad"/>
      <stop offset="1" stop-color="#cbd1d6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-55" x1="938.61" y1="337.13" x2="938.61" y2="339.16" xlink:href="#linear-gradient-54"/>
    <linearGradient id="Silver_04-9" data-name="Silver 04" x1="895.15" y1="304.74" x2="893.35" y2="304.74" xlink:href="#Silver_04"/>
    <linearGradient id="Silver_04-10" data-name="Silver 04" x1="938.6" y1="272.34" x2="938.6" y2="270.3" xlink:href="#Silver_04"/>
  </defs>
  <g class="cls-12">
    <g id="Layer_1" data-name="Layer 1">
      <g id="Layer_1-2" data-name="Layer 1">
        <path class="cls-20" d="m757.25,472.05c211.55,0,383.04-15.63,383.04-34.92s-171.49-34.92-383.04-34.92-383.04,15.63-383.04,34.92,171.49,34.92,383.04,34.92Z"/>
        <path class="cls-110" d="m214.08,347.43c-1.03-4.6-1.5-9.49.94-13.78,1.03-1.86,2.65-3.31,4.6-4.13,6.29-2.54,11.93,3.01,15.02,8.45,2.43,4.13,4.42,8.74,8.64,11.46,1.62,1.07,3.51,1.66,5.45,1.69,7.05-.19,9.02-8.11,6.76-13.61-1.97-4.6-5.92-7.9-9.39-11.46-9.22-9.3-15.97-20.76-19.63-33.34-2.26-7.89-3.11-17.02,3-23.39,3.19-3.24,8.11-4.69,12.31-3.01,5.26,2.07,6.19,7.14,7.05,11.84.66,3.85,1.62,8.11,4.86,10.43,2.16,1.5,5.16,1.78,7.14.1s1.78-4.32,1.69-6.67c-.8-15,4.31-29.71,14.23-40.99,5.36-6.1,14.66-11.17,21.22-5.73,1.7,1.43,2.94,3.32,3.58,5.45,1.5,5.08.37,10.15-.75,15.12l-6.29,27.14c-.31,1.58.69,3.12,2.25,3.48.78.18,1.61,0,2.25-.47l18.87-12.97c1.74-1.23,3.79-1.98,5.92-2.16,6.96-.47,8.92,5.54,9.02,10.71.19,11.27-5.82,21.97-13.9,29.96s-18.03,13.78-27.71,19.72c-3,1.88-6.19,3.85-7.23,7.42-.7,2.02-.15,4.26,1.41,5.73,2.72,2.34,6.49.75,9.3-1.13,8.64-5.73,15.87-13.78,26.49-16.16,3.74-.93,7.68-.67,11.27.75,11.08,4.51,10.54,16.34,5.26,23.95-5.55,7.99-13.78,13.24-21.61,18.78-15.78,11.08-37.66,31.18-58.79,24.79-12.43-3.6-23.29-13.13-31.07-22.98-5.83-7.33-9.99-15.86-12.16-24.98Z"/>
        <path class="cls-4" d="m257.41,384.62c.09-2.25.37-4.69,2.07-6.39.67-.78,1.61-1.29,2.63-1.41,3.29-.46,5.26,2.82,6.1,5.73.66,2.25,1.04,4.6,2.72,6.49.62.7,1.44,1.19,2.35,1.41,3.38.75,5.16-2.81,4.69-5.63-.37-2.34-1.88-4.41-3.1-6.49-3.27-5.47-5.11-11.67-5.36-18.03-.09-4.05.57-8.36,4.13-10.71,1.82-1.31,4.28-1.31,6.1,0,2.25,1.62,2.07,4.13,1.97,6.39-.09,1.87-.19,3.94,1.13,5.54.82.99,2.2,1.32,3.38.81,1.13-.56,1.32-1.87,1.62-3,1.39-7.17,5.5-13.53,11.46-17.75,3.19-2.25,8.16-3.57,10.61-.28.62.89.97,1.93,1.03,3.01.1,2.54-1.03,4.86-2.16,7.05l-6.1,12.11c-.35.68-.08,1.51.6,1.85.02,0,.04.02.06.03.35.17.75.2,1.13.1l10.33-3.95c.95-.4,1.99-.53,3.01-.37,3.28.56,3.57,3.66,3,6.1-1.22,5.35-5.25,9.67-10.04,12.58s-10.05,4.42-15.31,6.1c-1.62.57-3.38,1.13-4.23,2.63-.56.85-.56,1.96,0,2.81,1.04,1.41,2.92,1.13,4.51.57,4.7-1.69,9.02-4.7,14.28-4.6,1.86-.02,3.67.57,5.16,1.69,4.7,3.38,3.01,8.92-.28,11.93-3.58,3.1-7.99,4.69-12.31,6.39-8.64,3.47-21.22,10.42-30.43,4.97-5.36-3.19-9.4-8.92-11.93-14.46-1.97-4.12-2.94-8.64-2.84-13.21Z"/>
        <path class="cls-108" d="m214.08,347.43c-1.03-4.6-1.5-9.49.94-13.78,1.03-1.86,2.65-3.31,4.6-4.13,6.29-2.53,11.93,3.01,15.02,8.46,2.43,4.13,4.42,8.74,8.64,11.46,1.61,1.08,3.51,1.67,5.45,1.69,7.05-.19,9.02-8.11,6.76-13.62-1.97-4.6-5.92-7.89-9.39-11.46-9.22-9.3-15.97-20.77-19.63-33.34-2.26-7.89-3.11-17.02,3-23.38,3.19-3.29,8.11-4.69,12.31-3.01,5.26,2.07,6.19,7.13,7.05,11.84.66,3.85,1.62,8.11,4.86,10.43,2.16,1.5,5.16,1.78,7.14.09s1.78-4.32,1.69-6.66c-.8-15,4.31-29.72,14.23-41,5.36-6.1,14.66-11.18,21.22-5.73,1.7,1.43,2.94,3.32,3.58,5.45,1.5,5.08.37,10.14-.75,15.12l-6.29,27.14c-.33,1.59.68,3.15,2.26,3.5.78.18,1.61,0,2.25-.47l18.87-12.97c1.74-1.23,3.79-1.98,5.92-2.16,6.96-.47,8.92,5.54,9.02,10.7.19,11.27-5.82,21.98-13.9,30s-18.03,13.78-27.71,19.72c-3,1.87-6.19,3.85-7.23,7.42-.7,2.02-.15,4.26,1.41,5.72,2.72,2.35,6.49.75,9.3-1.13,8.64-5.72,15.87-13.78,26.49-16.15,3.74-.93,7.68-.67,11.27.75,11.08,4.51,10.54,16.34,5.26,23.95-5.55,7.98-13.78,13.24-21.61,18.78-15.78,11.08-37.66,31.18-58.79,24.79-12.44-3.66-23.3-13.17-31.08-23.03-5.84-7.34-9.99-15.87-12.16-24.99Z"/>
        <path class="cls-97" d="m270.65,423.5c-.28.02-.53-.19-.56-.47-3.86-50.27-1.02-100.84,8.45-150.36,0-.34.27-.62.61-.63.34,0,.62.27.63.61,0,.04,0,.07,0,.11v.1c-9.47,49.43-12.32,99.9-8.46,150.08-.06.32-.33.55-.66.57Z"/>
        <path class="cls-97" d="m272.81,318.22c-3.43.03-6.84-.58-10.05-1.79-7.89-3-13.34-9.39-16.53-14.18-4.05-6.19-6.85-13.34-9.2-19.72-.11-.29.04-.62.34-.73.26-.1.56.01.69.26,2.43,6.3,5.08,13.34,9.11,19.54,3.1,4.69,8.36,10.8,15.97,13.71,9.49,3.58,21.78,1.32,32.02-5.91,9.39-6.67,16.15-16.06,22.7-25.17.19-.19.57-.28.75-.1s.28.57.1.75c-6.67,9.2-13.53,18.69-23.02,25.45-6.62,4.95-14.62,7.71-22.89,7.9Z"/>
        <path class="cls-97" d="m266.52,379.26c-3.61,0-7.19-.57-10.61-1.69-13.53-4.51-22.16-17.56-27.43-26.96-.15-.26-.07-.6.19-.75.26-.15.6-.07.75.19h0c5.16,9.2,13.61,21.98,26.75,26.39,11.27,3.75,25.08.81,36.81-7.79l2.26-1.69c10.04-7.3,19.53-14.18,29.4-24.32.19-.19.56-.1.75.09.18.17.19.46.01.64,0,0,0,0-.01.01-9.96,10.24-19.53,17.19-29.58,24.52l-2.25,1.69c-8.35,6.39-18.02,9.68-27.04,9.68Z"/>
        <path class="cls-124" d="m257.41,384.62c.09-2.25.37-4.7,2.07-6.39.66-.79,1.6-1.29,2.63-1.41,3.29-.47,5.26,2.81,6.1,5.73.66,2.25,1.04,4.6,2.72,6.49.62.69,1.44,1.19,2.35,1.41,3.38.75,5.16-2.82,4.69-5.63-.37-2.35-1.88-4.42-3.1-6.49-3.27-5.47-5.11-11.67-5.36-18.03-.09-4.05.57-8.36,4.13-10.71,1.82-1.31,4.28-1.31,6.1,0,2.25,1.62,2.07,4.13,1.97,6.39-.09,1.88-.19,3.95,1.13,5.55.82.99,2.2,1.32,3.38.81,1.13-.57,1.32-1.88,1.62-3.01,1.39-7.17,5.5-13.53,11.46-17.75,3.19-2.25,8.16-3.58,10.61-.28.62.89.98,1.93,1.03,3.01.1,2.53-1.03,4.86-2.16,7.05l-6.1,12.11c-.35.68-.08,1.51.6,1.85.02,0,.04.02.06.03.35.17.75.2,1.13.09l10.33-3.94c.95-.41,1.99-.54,3.01-.38,3.28.57,3.57,3.66,3,6.1-1.22,5.36-5.25,9.68-10.04,12.59s-10.05,4.41-15.31,6.1c-1.62.56-3.38,1.13-4.23,2.63-.56.86-.56,1.96,0,2.82,1.04,1.4,2.92,1.13,4.51.56,4.7-1.69,9.02-4.69,14.28-4.6,1.86-.02,3.67.58,5.16,1.69,4.7,3.38,3.01,8.92-.28,11.93-3.58,3.11-7.99,4.69-12.31,6.39-8.64,3.48-21.22,10.43-30.43,4.98-5.36-3.19-9.4-8.92-11.93-14.46-1.97-4.12-2.94-8.65-2.84-13.21Z"/>
        <path class="cls-97" d="m275.07,427.35c-.3-.09-.52-.35-.57-.66,4.06-24.25,11.28-47.87,21.46-70.25.1-.3.42-.46.72-.36.3.1.46.42.36.72-.01.04-.03.08-.05.11-10.21,22.26-17.39,45.8-21.32,69.97-.02.29-.27.5-.56.48-.01,0-.03,0-.04,0Z"/>
        <path class="cls-97" d="m292.34,378.32c-3.1.06-6.14-.83-8.73-2.54-3.38-2.35-5.26-6.1-6.2-8.73-1.07-3.39-1.76-6.89-2.06-10.43-.1-.28.09-.66.37-.66s.66.09.66.37v.1c.29,3.47.95,6.89,1.97,10.21,1.01,3.26,3.01,6.11,5.73,8.17,3.95,2.72,9.86,3.1,15.4.81,5.07-2.06,9.39-5.72,13.52-9.2.19-.19.56-.09.75.1.16.24.12.56-.1.75-4.22,3.58-8.54,7.3-13.78,9.4-2.37,1.08-4.94,1.64-7.54,1.65Z"/>
        <path class="cls-97" d="m282.86,406.59c-3.35.08-6.66-.83-9.49-2.63-5.92-3.75-8.45-10.99-9.86-16.06-.1-.28.04-.58.32-.69.28-.1.58.04.69.32.01.03.02.06.02.09,1.31,4.86,3.76,11.83,9.4,15.4,4.79,3,11.46,3.19,17.93.56l1.22-.47c5.79-2.16,11.37-4.86,16.64-8.08.23-.16.54-.11.7.11.02.02.03.05.04.07.16.23.11.55-.12.71-.02.02-.04.03-.07.04-5.33,3.19-10.92,5.93-16.72,8.17l-1.22.46c-3.02,1.23-6.24,1.9-9.5,1.99Z"/>
        <path class="cls-90" d="m270.56,396.07c-.28-1.59-1.12-3.02-2.35-4.05-.57-.41-1.27-.61-1.97-.56-2.25.19-3.1,2.72-3.29,4.79-.09,1.62,0,3.28-.81,4.78-.32.58-.81,1.04-1.41,1.32-2.16,1.03-3.94-1.13-4.05-3.11.09-1.67.47-3.32,1.13-4.86,1.38-4.24,1.7-8.75.92-13.13-.47-2.72-1.69-5.67-4.41-6.66-1.44-.57-3.08-.2-4.13.93-1.22,1.41-.81,3.11-.37,4.7.37,1.31.75,2.72.09,3.94-.4.8-1.27,1.26-2.16,1.13-.81-.19-1.22-1.03-1.51-1.78-2.05-4.7-5.82-8.43-10.54-10.43-2.54-1.03-6.1-1.22-7.3,1.41-.28.68-.38,1.43-.28,2.16.41,1.71,1.29,3.27,2.54,4.51l6.02,7.3c.34.4.29,1.01-.11,1.35-.03.02-.05.04-.08.06-.19.2-.48.28-.75.19l-7.7-1.13c-.69-.13-1.41-.06-2.07.19-2.16.94-1.88,3.01-1.13,4.6,1.69,3.47,5.08,5.82,8.73,7.05s7.52,1.5,11.35,1.87c1.22-.04,2.4.4,3.29,1.22.47.54.64,1.28.47,1.97-.47,1.13-1.88,1.22-3.01,1.04-3.48-.47-6.86-1.78-10.43-.94-1.26.29-2.4.94-3.29,1.87-2.63,3.01-.75,6.57,2.07,8.11s6.2,1.97,9.4,2.54c6.49,1.03,16.06,3.85,21.51-1.22,3.19-3.01,5.07-7.52,5.91-11.65.57-3.15.48-6.38-.27-9.49Z"/>
        <path class="cls-51" d="m270.56,396.07c-.28-1.59-1.12-3.02-2.35-4.05-.57-.41-1.27-.61-1.97-.56-2.25.19-3.1,2.72-3.29,4.79-.09,1.62,0,3.28-.81,4.78-.32.58-.81,1.04-1.41,1.32-2.16,1.03-3.94-1.13-4.05-3.11.09-1.67.47-3.32,1.13-4.86,1.38-4.24,1.7-8.75.92-13.13-.47-2.72-1.69-5.67-4.41-6.66-1.44-.57-3.08-.2-4.13.93-1.22,1.41-.81,3.11-.38,4.7.38,1.31.75,2.72.1,3.94-.4.8-1.27,1.26-2.16,1.13-.81-.19-1.22-1.03-1.51-1.78-2.05-4.7-5.82-8.43-10.54-10.43-2.54-1.03-6.1-1.22-7.3,1.41-.28.68-.38,1.43-.28,2.16.41,1.71,1.29,3.27,2.54,4.51l6.02,7.3c.34.4.29,1.01-.11,1.35-.03.02-.05.04-.08.06-.19.2-.48.28-.75.19l-7.7-1.13c-.69-.13-1.41-.06-2.07.19-2.16.94-1.88,3.01-1.13,4.6,1.69,3.47,5.08,5.82,8.73,7.05s7.52,1.5,11.35,1.87c1.22-.04,2.4.4,3.29,1.22.47.54.64,1.28.46,1.97-.46,1.13-1.87,1.22-3,1.04-3.48-.47-6.86-1.79-10.43-.94-1.26.29-2.4.94-3.29,1.87-2.63,3.01-.75,6.57,2.07,8.11s6.2,1.97,9.4,2.54c6.49,1.03,16.06,3.85,21.51-1.22,3.19-3.01,5.07-7.52,5.91-11.65.57-3.15.48-6.38-.27-9.49Z"/>
        <path class="cls-97" d="m252.99,415.51c-1.07,0-2.13-.09-3.19-.28l-.94-.19c-4.31-.65-8.55-1.69-12.68-3.1-.29-.07-.46-.37-.38-.65,0,0,0,0,0,0,.12-.3.44-.46.75-.37,4.08,1.33,8.26,2.33,12.49,3l.94.19c4.79.81,9.2-.28,12.02-3.01,3.19-3.19,3.84-8.27,4.05-11.84.02-.3.26-.54.56-.56.3.02.55.26.57.56-.19,3.76-.85,9.02-4.32,12.59-2.7,2.45-6.24,3.76-9.88,3.66Z"/>
        <path class="cls-97" d="m264.83,428.19c-.19,0-.47-.09-.47-.37-6.37-15.86-14.83-30.8-25.15-44.43-.16-.24-.12-.56.1-.75.24-.16.56-.12.75.09,10.35,13.73,18.84,28.77,25.24,44.73.1.28-.02.6-.28.75l-.19-.02Z"/>
        <path class="cls-97" d="m242.76,397.58c-.85-.02-1.7-.08-2.54-.19-3.95-.66-7.52-2.54-10.9-4.32-.25-.18-.3-.52-.12-.77.16-.22.45-.29.69-.17,3.38,1.78,6.76,3.66,10.54,4.22,4.05.66,7.99-.47,10.24-2.91,1.52-1.8,2.44-4.04,2.63-6.39.17-2.41.11-4.83-.19-7.23-.05-.28.14-.55.42-.6.28-.05.55.14.6.42,0,.03,0,.06,0,.09.3,2.49.36,5,.19,7.51-.19,2.6-1.22,5.07-2.92,7.05-2.35,2.19-5.45,3.37-8.66,3.29Z"/>
        <path class="cls-18" d="m268.78,446.69c14.41,0,26.1-1.18,26.1-2.63s-11.69-2.63-26.1-2.63-26.11,1.18-26.11,2.63,11.69,2.63,26.11,2.63Z"/>
        <path class="cls-30" d="m280.42,423.21h-23.29c-.71-.02-1.3.54-1.31,1.25,0,.02,0,.04,0,.07v.28l.81,3.76c.11.63.68,1.08,1.31,1.03h21.51c.62-.02,1.15-.44,1.31-1.03l.85-3.76c.19-.64-.18-1.3-.81-1.49-.01,0-.03,0-.04-.01-.1-.07-.22-.1-.34-.1Z"/>
        <path class="cls-98" d="m258.35,430.26l1.87,12.97c.15.87.91,1.51,1.79,1.51h13.61c.87-.03,1.61-.65,1.78-1.51l1.88-12.97h-20.94Z"/>
        <path class="cls-101" d="m589.77,209.15l-2.43-3.14s-1.37-.96-3.83.68c-2.01,1.6-3.67,3.6-4.86,5.87-.28.79-.13,1.67.41,2.32.81.68,11.46,2.04,11.46,2.04l-.75-7.77Z"/>
        <path class="cls-136" d="m587,206.56l-.54-.96c-.85-.39-1.85-.29-2.59.28-2.03,1.6-3.7,3.59-4.91,5.87-.28.79-.12,1.67.41,2.32.81.68,9.27-3,9.27-3l-1.64-4.51Z"/>
        <path class="cls-5" d="m587.31,206.43l-.81-.81c-.85-.39-1.85-.29-2.59.28-2.03,1.6-3.7,3.59-4.91,5.87-.31.81-.21,1.72.28,2.43.81.68,9.27-3.01,9.27-3.01l-1.23-4.76Z"/>
        <path class="cls-5" d="m586.49,205.6s1.62,1.5-1.37,5.19c-3.54,4.23-5.86,3-5.86,3l1.78,2.73,8.32-4.51-.54-4.36-2.32-2.05Z"/>
        <path class="cls-68" d="m585.38,204.92c-.32-1.91-1.07-3.73-2.18-5.32-1.62-2.32-3.24-2.43-5.46-4.64-1.13-1.24-2.36-2.39-3.69-3.41,0,0-2.18.81-.27,3.14,3.96,4.36,3.24,4.5,3.24,4.5,0,0,0,.14-.28,0-2.2-.52-4.35-1.25-6.41-2.18-3.14-1.91-4.78-3.14-5.59-3.24-.44-.09-.86.2-.95.64,0,.01,0,.03,0,.04v.41c0,.14-.96-.55-1.36-.41-1.09.14-.81,1.23-.68,1.62,0,.14-.28,0-.55.55-.34.63-.22,1.4.28,1.91-.53.56-.59,1.42-.14,2.05,2.79,3.32,6.3,5.98,10.26,7.76,2.79.93,5.67,1.53,8.6,1.78,0,0,9.82,11.59,16.51,15.55.68.41,3.96,1.36,4.91.68s3.24-1.78,1.09-4.23c-4.9-5.43-17.32-17.19-17.32-17.19Z"/>
        <path class="cls-41" d="m626.33,414.62l5.18,1.49,2.32-3.54-3-.81-4.5,2.86Z"/>
        <path class="cls-104" d="m627.15,397.02s-.96,3.01-1.91,5.33c-.54,1.5-12.83,9.96-20.74,12.97-.68.28-.81.69-.81,2.59,1.4,1.01,3.05,1.62,4.78,1.78,2.67.41,5.38.41,8.05,0,4.98-1.07,9.74-3.01,14.06-5.73,0,0,.81-.41.81.41v1.74l8.32-3.24c.41-1.29.68-2.61.81-3.96.18-4.15-.05-8.31-.68-12.42l-12.68.53Z"/>
        <path class="cls-83" d="m621.86,259.64s3.55,66.17,3.96,79.13c.55,17.88-.41,59.76-.54,60.44-1.22,5.87,14.19,3.01,17.02.96,0,0,2.86-27.69,3.82-46.66.14-2.73-1.36-10.64-1.23-12.56,1.37-21.82,6.83-79.13,6.83-79.13l-29.85-2.19Z"/>
        <path class="cls-104" d="m666.71,404.12s1.36,13.37,1.62,19.37c.14,4.37-4.91,9.41-11.05,9.83-1.08.02-2.17-.02-3.24-.14-2.18,0-3.14-2.72-2.18-8.46.81-5.05,3.4-21.97,3.68-22.11l11.17,1.51Z"/>
        <path class="cls-83" d="m638.48,255.13c2.32,12.55,10.91,78.59,12.69,97.83,1.91,20.74.54,57.84.54,57.84-.14,2.59,6,4.78,6,4.78,8.87-.28,12.16-4.64,12.16-5.59,0,0,.55-10.91,1.5-36.56.28-8.81-.26-17.62-1.62-26.33,0-16.51.14-45.71.41-49.8.41-8.05-.68-15.96-.41-23.88.28-9.69-11.19-18.55-11.19-18.55l-20.08.27Z"/>
        <path class="cls-23" d="m654.84,265.64l12.42-.96c1.61,2.63,2.51,5.64,2.59,8.73-.27,7.92.81,15.83.41,23.88-.28,4.05-.41,33.29-.41,49.79,1.36,8.71,1.9,17.52,1.62,26.33-.96,25.65-1.5,36.56-1.5,36.56,0,.68-2.18,3-7.91,3.96.28-14.59,1.22-68.63.28-96.32-1.09-25.65-7.5-51.98-7.5-51.98Z"/>
        <path class="cls-101" d="m587.31,206.02s18.01,15.01,18.82,12.97c3.68-9.41,3.24-14.2,5.87-25.79,3.54-15.96,10.09-21.14,17.19-19.78,4.09.81,7.64-.96,7.77,6.55s-14.73,44.2-21.28,53.21c-4.64,6.27-9.27,3.82-9.27,3.82,0,0-18.15-9.41-27.43-22.1-.27-.28,2.43,1.78,6.96-3.14,1.23-1.65,2.46-4.81,1.37-5.74Z"/>
        <path class="cls-5" d="m674.63,206.29s.54-17.88-17.74-29.19c-11.73-7.23-20.6-2.86-20.6-2.86-.68-.14-19.78,3.68-18.55,25.51,2.04,33.02,3.82,58.8,3.82,58.8,0,0-.41,2.32,2.32,4.91,2.32,2.18,11.19,7.23,16.78,9.27,10.37,3.69,26.75-.54,28.37-3.24s-5.59-15.27-3.96-27.56,9.55-35.65,9.55-35.65Z"/>
        <path class="cls-52" d="m657.16,177.23s11.73,8.05,16.21,12.27c4.22,3.96,8.05,8.19,7.5,12.16-1.11,5.36-2.71,10.62-4.78,15.69-2.86,7.37-6.54,38.61-6.41,40.93,1.5,15.4,2.32,21.42,2.32,21.42,0,0-13.51,3.41-26.06,6.01-1.5.27-1.62-.81-1.62-.81,0,0-1.78-27.7-1.78-48.03,0-8.33,11.74-41.61,14.33-48.43,2.74-6.57,1.92-9.99.28-11.21Z"/>
        <path class="cls-27" d="m645.29,172.9s-19.64,18.52-19.92,46.21c-.14,18.83.54,60.03.54,60.03,0,0-.14.96-.81.14-1.36-1.5-5.32-6.96-5.32-6.96,0,0-.68-22.51-1.5-39.84s-1.92-42.71,4.39-53.1c3.41-5.59,14.88-5.59,12.97-5.87l9.65-.62Z"/>
        <path class="cls-68" d="m656.72,164.79v14.75c-.49,4.59-4.11,8.23-8.7,8.76-6.96,1.09-9.96-6.82-9.96-6.82l3.82-15.7c.14-.02,14.84-5.2,14.84-1Z"/>
        <path class="cls-68" d="m663.44,280.78s-5.86,3.68-7.77,6.14c-.44.57-.85,1.16-1.22,1.78l9.14,6.83s5.46-6.83,5.59-10.37-5.73-4.37-5.73-4.37Z"/>
        <path class="cls-3" d="m656.72,174.91c.36,0,.71.14.95.41,2.05,2.05,5.87,6.15,5.87,6.15l-4.06,23.06c-.05.55-.34,1.06-.81,1.36l-6.41,2.86,2.73,4.65c.96,1.5.81,1.22.14,2.18-8.33,13.51-12.42,21.96-12.42,21.96.15-10.47,1.9-20.86,5.19-30.81,1.77-5.46,5.32-16.51,7.09-22.78,1.06-2.9,1.64-5.95,1.73-9.03Z"/>
        <path class="cls-52" d="m643.79,171.77s-19.92,18.65-20.19,41.88c-.28,18.83.28,62.35.28,62.35,0,0-.14.96-.81.14-1.36-1.5-5.32-6.96-5.32-6.96,0,0,.13-24.97-1.09-42.29-1.23-18.28-3.14-37.93,3.82-50.07,1.51-2.78,4.35-4.59,7.51-4.78,6.27.13,15.82-.28,15.82-.28Z"/>
        <path class="cls-3" d="m635.2,172.09s-10.37,13.51-13.51,18.55c-.39.67-.44,1.48-.14,2.19l2.59,4.78-4.65,4.64,3.82,28.65s.55-21.42,2.87-29.19,16.21-27.28,15.96-29.74l-6.96.12Z"/>
        <path class="cls-23" d="m662.48,217.49c4.05-10.36,16.92-20.19,16.51-16.21-.41,3,0,9.14-2.73,16.21-2.86,7.37-6.55,38.61-6.41,40.93,1.5,15.4,2.32,21.42,2.32,21.42,0,0-9.27,2.32-19.64,4.65,0-7.78.81-31.52,1.09-37.29.41-9.39,2.29-13.21,8.86-29.7Z"/>
        <path class="cls-60" d="m659.89,281.33l-2.32,2.72c2.13,3.49,5.27,6.25,9,7.91,2.87,1.09,1.5.55,1.5.55l2.33-3.41-10.51-7.77Z"/>
        <path class="cls-43" d="m658.39,282.28s18.41-24.96,17.19-35.47c-1.09-8.6-5.05-18.28-9.27-29.19-5.73-15.14.81-26.75,7.09-26.06,5.59.54,8.46,8.19,11.59,19.24,2.73,9.55,8.46,30.81,7.77,38.74-1.5,15.7-23.6,42.16-23.6,42.16-1.7-.82-3.3-1.82-4.78-3-2.24-1.9-4.26-4.06-6-6.43Z"/>
        <path class="cls-23" d="m686.36,249.53c0-.54-5.32.14-5.32-.54,0-.41,4.37-.55,5.73-2.73.28-.41-2.05,0-4.37,0-2.04-.14-4.05-.68-3.54-.96,2.04-.81,8.19-3.24,7.77-6.54-1.5-11.87-5.73-30.7-9.69-41.35-1.91-5.05-5.32-5.87-3.14-5.87,5.19.96,8.05,8.46,11.19,19.1,2.73,9.55,8.46,30.81,7.77,38.74-1.5,15.7-23.6,42.16-23.6,42.16-1.7-.81-3.3-1.82-4.78-3,4.64-7.49,21.14-33.42,21.96-39.01Z"/>
        <path class="cls-22" d="m656.72,169.45c-.28.41-9.96,11.59-13.38,11.35-2.02-.26-3.57-1.93-3.68-3.96,4.05-6.68,2.32-8.59,2.32-8.59l14.74-3v4.21Z"/>
        <path d="m659.08,163.45c12.83.81,32.33-31.79,7.64-42.7-29.47-13.1-36.98,28.93-27.69,35.34,4.91,3.4,14.87,7.09,20.06,7.36Z"/>
        <path class="cls-73" d="m643.11,176.27c.61.24,1.26.33,1.91.28.95-.13,1.87-.4,2.73-.81,1.56-.93,3.02-2.01,4.36-3.24.81-.68,1.51-1.5,2.33-2.32.98-1.13,1.89-2.31,2.72-3.55.81-.96,1.49-2.01,2.04-3.14.86-1.41,1.63-2.87,2.33-4.36.72-1.56,1.35-3.15,1.91-4.78.41-1.37.81-2.87,1.09-4.23.14-.96.41-1.78.54-2.73.11-.63.16-1.27.14-1.91v-1.95c-.03-.46-.12-.92-.28-1.36-.31-.97-.77-1.9-1.36-2.73-.94-1.09-2.17-1.89-3.54-2.32-1.58-.53-3.24-.81-4.91-.81l-12.01,39.97Z"/>
        <path class="cls-68" d="m639.29,170.55c.03.38.12.74.28,1.09.14.28.14.68.28.96.39,1.35,1.2,2.55,2.32,3.41l.81.41,12.28-38.37c.41-.96-.13-1.5-.27-1.5-1.14-.09-2.29-.05-3.41.14-1.36.11-2.7.43-3.96.96-1.38.55-2.63,1.38-3.68,2.43-1.02,1.14-1.76,2.49-2.19,3.96-.54,1.91-1.22,3.82-1.62,5.72-.41,1.62-.68,3.24-.95,4.78-.14,1.09-.28,2.32-.41,3.4-.11.86-.16,1.73-.14,2.59v2.04c-.02.64.02,1.28.14,1.91v1.62c-.02.28.03.56.14.81.12,1.31.26,2.4.39,3.63Z"/>
        <path class="cls-128" d="m779.35,312.82h-44.2c-.68-.08-1.29.41-1.37,1.09v.13c.11.68.68,1.2,1.37,1.23h19.24v40.11h2.86v-40.11h22.1c.68.07,1.29-.42,1.37-1.09v-.14c-.11-.68-.68-1.19-1.37-1.22Z"/>
        <path class="cls-105" d="m760.11,356.61h-5.72v4.23h5.72v-4.23Z"/>
        <path class="cls-66" d="m774.99,402.73h-14.88v-40.93h-5.72v40.93h-14.88c-.59,0-1.08.46-1.09,1.05,0,.01,0,.03,0,.04.04.58.5,1.05,1.09,1.09h14.88v19.38h5.72v-19.38h14.88c.6-.05,1.05-.58,1-1.18-.05-.53-.47-.95-1-1h0Z"/>
        <path class="cls-62" d="m754.39,425.38l-18.42,3.54h42.56l-18.42-3.54h-5.72Z"/>
        <path class="cls-82" d="m777.99,312.82h-41.61c-3.73.11-6.85-2.83-6.95-6.56,0-.04,0-.08,0-.12v-2.04c.08-3.79,3.17-6.82,6.96-6.83h41.61c3.82,0,6.95,3.01,7.09,6.83v1.91c-.04,3.81-3.17,6.87-6.99,6.83-.04,0-.07,0-.11,0h0Z"/>
        <path class="cls-88" d="m433.64,297.75c1.37,6.27,2.32,9.14,9.14,20.6,9.27,15.88,14.19,36.15,14.5,38.49.54,7.91,5.04,23.33,11.59,35.61,5.19,9.83,15.4,27.15,15.4,27.15,2.66,3.9,5.56,7.64,8.67,11.19,3.24,3.41,11.73,7.64,12.83,5.59.41-.96-.28-1.09-1.37-1.77-1.5-.96-5.72-3.69-13.51-22.25-1.5-3.68-12.55-35.06-17.6-55.25-.54-2.32,0-5.46-1.5-9.83-3.14-9.27-19.1-58.94-19.1-58.94,0,0-22.75-8.59-19.07,9.4Z"/>
        <path class="cls-21" d="m496.13,433.52c-6.83-8.59-17.2-33.43-23.33-46.66-4.5-9.73-6.96-18.69-6-23.06.41-1.91,3.24,1.23,1.22-2.32-3-4.91-16.21-50.07-25.78-73.26-6.41,3-8.19,7.77-8.19,13.92,1.5,4.64,4.09,8.18,8.87,16.09,9.27,15.83,14.19,36.16,14.46,38.48.02.73.11,1.46.27,2.18.91,1.76,1.69,3.58,2.32,5.46.27,3.97.96,7.91,2.05,11.74,1.92,5.62,4.24,11.09,6.96,16.37,5.18,9.83,15.4,27.15,15.4,27.15,2.68,3.9,5.59,7.64,8.73,11.19.83,1.09,1.85,2.01,3.02,2.73Z"/>
        <path class="cls-53" d="m467.21,392.86c3.41,6.41,9.83,15.83,13.24,21.69,3.58-.25,7.11-.99,10.5-2.19-2.59-6.68-12.97-36.01-17.6-54.98-.54-2.32,0-5.46-1.5-9.82-2.04-6.15-4.5-27.97-13.51-50.21-1.22-3-4.64-17.74-4.64-17.74,0,0-23.6.28-21.14,17.46.81,6.01,3.68,11.35,9.96,23.88,8.19,16.51,13.1,33.97,13.24,36.3.41,7.78,4.9,23.19,11.46,35.61Z"/>
        <path class="cls-25" d="m467.21,392.86c3.41,6.41,9.83,15.83,13.24,21.69,2.06-.19,4.11-.51,6.14-.96-7.37-11.87-19.65-37.38-20.74-53.51-.14-2.32,1.62-6.55.14-10.91-2.59-7.78-17.33-52.53-23.74-67.29-6.67,1.81-10.95,8.3-9.96,15.14.81,6,3.68,11.35,9.96,23.88,8.18,16.51,13.09,33.97,13.23,36.3.68,7.83,5.18,23.23,11.73,35.65Z"/>
        <path class="cls-31" d="m483.58,418.78c-1.67,2.82-1.35,6.4.81,8.87.81.96,3.14,10.37,3.14,10.37,0,0,0,.14.27.14.15-.01.26-.13.28-.28,0,0-1.78-8.59-1.78-9.14,0-2.73,3.41,1.91,4.64,3.41,3.55,4.36,5.87,7.09,7.64,7.5,3,.68,10.09-1.09,10.54-1.36s.54-1.78-1.23-2.32c-1.38-.5-2.71-1.14-3.96-1.91-1.03.3-2.08.52-3.14.68-2.86.28-4.78-.68-9.41-6.41-5.21-6.14-7.8-9.55-7.8-9.55Z"/>
        <path class="cls-133" d="m455.2,310.31h-47.75c-.75.06-1.32.72-1.26,1.47.05.67.59,1.21,1.26,1.26h20.88v43.52h3.02v-43.52h23.88c.75-.06,1.32-.72,1.26-1.47-.05-.67-.59-1.21-1.26-1.26h-.02Z"/>
        <path class="cls-134" d="m432.97,357.66h-6.27v4.5h6.27v-4.5Z"/>
        <path class="cls-48" d="m449.06,407.59h-16.09v-44.18h-6.27v44.2h-16.11c-.61-.06-1.16.38-1.22.99,0,.03,0,.07,0,.1,0,.59.46,1.08,1.05,1.09.01,0,.02,0,.03,0h16.21v20.99h6.31v-21.01h16.1c.61.06,1.16-.38,1.22-.99,0-.03,0-.06,0-.1-.19-.56-.66-.97-1.23-1.09Z"/>
        <path class="cls-42" d="m426.69,432.15l-19.92,3.83h46.11l-19.91-3.83h-6.28Z"/>
        <path class="cls-82" d="m453.56,310.31h-45.02c-4.13.09-7.55-3.19-7.64-7.32,0-.02,0-.03,0-.05v-2.04c.07-4.14,3.47-7.44,7.61-7.37.01,0,.02,0,.03,0h45.02c4.13-.09,7.55,3.19,7.64,7.32,0,.02,0,.03,0,.05v2.04c-.07,4.14-3.47,7.44-7.61,7.37-.01,0-.02,0-.03,0Z"/>
        <path class="cls-88" d="m459.16,395.32c.81-1.37,8.87-12.56,14.33-19.79l-9-6c-4.51,7.64-10.37,19.51-11.06,19.78-5.32,1.22-4.5,10.78-4.5,10.78,0,0-3.24,8.18-1.91,12.69.55,1.77,5.59,8.18,5.73,3.96s1.91-14.46,6.41-21.42Z"/>
        <path class="cls-21" d="m448.92,414.69c.52.28,1.06.51,1.62.68.27-7.47,2.18-14.78,5.59-21.43,1.62-3.24,8.73-11.18,13.78-17.32l-7.37-2.59c-2.59,4.65-5.46,9.73-7.23,12.7-.96,1.62-1.5,2.59-1.78,2.59-5.32,1.23-4.5,10.78-4.5,10.78-1.35,2.86-1.76,10.64-.12,14.6Z"/>
        <path class="cls-31" d="m453.7,389.31s-5.18-1.62-6.41,6.14c-.14.96-8.19,7.91-8.19,7.91l.55.68s7.36-6.4,7.77-6.82c1.22-1.22.54.81.28,1.78-.96,3.54-1.91,7.09-2.32,9.27s-.69,4.09.41,5.73c2.32,3.41,5.05,6.82,5.73,7.09,1.22.54,1.62,0,1.78-2.32.13-4.09.13-3.14.13-3.14-.25-1.63-1.19-3.08-2.59-3.96-2.43-1.36-2.87-1.5-.96-9.96.79-4.27,2.08-8.43,3.82-12.41Z"/>
        <path class="cls-53" d="m448.92,262.98s-27.01,3.68-28.51,13.09,5.18,19.79,13.09,27.97c0,0,8.33,7.91,19.92,17.46,15.56,12.83,26.75,22.25,26.47,22.65-1.36,1.91,2.73,1.91,1.62,3.14-5.14,4.61-9.64,9.88-13.37,15.69-9.01,13.24-13.1,21.01-12.97,21.15.28.81,3.41,7.37,8.73,7.77,5.19-5.46,21.15-23.88,34.52-35.61,4.23-3.69,6.27-12.28-.54-21.97-4.09-5.86-25.51-35.88-43.53-53.75-1.06-1.09-5.43-17.6-5.43-17.6Z"/>
        <path class="cls-5" d="m420.81,258.74c.81,3,.55,8.46-1.77,13.78-.41,1.22-1.09,1.62,1.62,3.82,3.96,3,10.64,5.87,20.6,3.41,5.18-1.23,7.91-1.78,10.77-3.83,1.78-1.5,2.32-2.04,1.91-4.05-1.14-5.49-1.87-11.05-2.18-16.64,0-3.96.68-6.55,2.59-9.01,3.96-4.78,5.87-7.37,2.87-17.02-.81-2.72-2.32-9.14-3.24-13.51-1.78-8.46-5.59-11.73-8.73-11.73-13.51-.41-24.01,1.91-37.38,11.19-9.96,6.96.27,18.96.27,18.96,0,0,10.64,16.72,12.67,24.63Z"/>
        <path class="cls-45" d="m420.95,209.63c1.49-5.1,2.31-10.38,2.43-15.7-.14-6.54,0-8.73,0-8.73l15.4,6.69s-1.09,13.23-1.09,17.19c0,.81,1.91,2.43.41,6.01-1.09,2.86-7.74,9.41-17.15-5.46Z"/>
        <path class="cls-22" d="m423.14,198.44c.28.41,2.87,7.51,9.28,11.06,2.18,1.22,4.36-2.32,5.18-3.55,1.62-8.32-2.72-8.73-2.72-8.73l-11.74-3c.14,1.4.14,2.82,0,4.22Z"/>
        <path class="cls-28" d="m421.91,243.6c-7.51-19.51-7.91-15.96-8.73-16.92-1.5-1.22-4.64-.68-4.78-.68-2.73,1.91-2.59,4.09-1.91,5.73.55.81,1.09,1.62,1.62,2.32,0,0,10.64,16.78,12.69,24.69.81,3,.55,8.46-1.77,13.78-.41,1.22-1.09,1.62,1.62,3.82,3.59,2.82,7.99,4.4,12.55,4.5-2.32-.68-4.37-2.04-3.55-5.73,1.78-8.05-1.09-19.78,2.19-20.46,3.55-.79,6.66-2.88,8.73-5.87,0,0-14.71,5.19-18.66-5.18Z"/>
        <path class="cls-28" d="m435.14,218.23c-.27.41.14.81.69,1.5l5.04,5.73c.55.41.96.28.96-1.22v-11.19c-.28-2.59-1.36-4.51-2.04-6.96-.24-.68-.81-1.19-1.51-1.36-.02,1.23-.11,2.46-.27,3.68-.54,3.38-1.5,6.68-2.87,9.83Z"/>
        <path class="cls-28" d="m421.62,204.31c-.15,1.77.28,3.55,1.23,5.05,2.86,4.36,12.16,8.87,12.16,8.87.68.41.95.54.81,1.91l-1.51,8.19c-.13.68-.68.81-1.22.28,0,0-11.46-10.78-13.23-12.83-2.05-2.32-2.73-4.23-2.33-6.01,1.24-1.9,2.61-3.73,4.09-5.46Z"/>
        <path class="cls-5" d="m435.14,216.99c-.27.41.14.81.69,1.5l4.78,5.73c.54.41.95.28.81-1.22l-.14-11.19c-.28-2.59-1.37-4.51-1.91-6.96-.14-.69-.67-1.23-1.36-1.37,0,0-.14,2.05-.28,3.69-.27,1.22-1.22,7.07-2.59,9.82Z"/>
        <path class="cls-5" d="m422.43,203.62c-.29.97-.38,2-.27,3.01.14.78.52,1.49,1.09,2.04,3.24,3.41,11.46,8.33,11.46,8.33.68.41.96.54.81,1.91l-1.5,8.19c-.14.68-.68.81-1.09.28,0,0-11.46-10.78-13.23-12.97s-2.43-3.69-2.05-5.46c.17-1.09,4.78-5.72,4.78-5.32Z"/>
        <path class="cls-17" d="m437.46,159.01q.14,0,.28-.14c.28-.14.14-.14-.28.14Z"/>
        <path d="m420.95,190.94c-12.69,0-30-33.56-4.91-42.97,30-11.19,34.93,30.97,25.25,36.84-5.08,3.13-15.14,6.13-20.33,6.13Z"/>
        <path class="cls-73" d="m436.1,204.72c-.62.18-1.27.23-1.91.14-.94-.16-1.82-.54-2.59-1.09-1.52-.99-2.89-2.18-4.09-3.54-.68-.81-1.5-1.62-2.04-2.43-.9-1.16-1.72-2.4-2.43-3.69-.68-1.09-1.37-2.04-1.91-3.14-.68-1.5-1.5-3-2.04-4.36-.67-1.59-1.21-3.24-1.62-4.91-.41-1.36-.55-2.87-.81-4.23-.14-.96-.28-1.78-.41-2.72v-1.91c0-.68.14-1.22.14-1.91.03-.46.12-.92.28-1.36.27-1.01.84-1.91,1.62-2.59,1.01-1.01,2.29-1.72,3.68-2.04,1.61-.38,3.26-.57,4.91-.54l9.24,40.36Z"/>
        <path class="cls-68" d="m440.19,199.26c-.14.41-.41,1.09-.41,1.09l-.41.81c-.42,1.35-1.35,2.48-2.59,3.14l-.81.41-9.68-38.74c-.28-.96.27-1.51.41-1.51,1.15,0,2.3.14,3.41.41,1.31.27,2.59.68,3.82,1.22,1.37.63,2.58,1.57,3.54,2.73.95,1.18,1.61,2.57,1.91,4.05.41,1.91.96,3.82,1.22,5.86.28,1.62.41,3.24.55,4.78.14,1.23.14,2.33.28,3.41v2.59c.02.68-.02,1.37-.14,2.04,0,.69-.14,1.23-.14,1.78s-.14,1.09-.14,1.62c0,.28-.14.54-.14.81-.28,1.17-.41,2.39-.69,3.49Z"/>
        <path class="cls-5" d="m466.25,238.01c-.95-4.64-9.96-20.32-12.27-26.47-2.32-6.15-6.14-8.05-10.54-7.5-.68,0,1.22.81,2.32,2.86,4.64,8.73,10.37,22.24,10.77,30.81.58,10.67,11.09,7.94,9.72.3Z"/>
        <path class="cls-7" d="m445.79,206.76c4.64,8.73,10.37,22.24,10.77,30.81.02,1.88.54,3.71,1.51,5.32,1.5-2.43,2.59-5.59,2.32-7.23-.81-4.64-6.27-15.69-9.28-21.28-1.64-2.56-3.4-5.16-5.32-7.61Z"/>
        <path class="cls-5" d="m430.92,240.06c6.54-2.73,20.46-5.05,27.01-6.28,4.23-.81,6.27.14,6.27.14,0,0,5.32,6.55-.41,10.78-5.19,3.82-31.24,5.59-31.24,5.59-1.4-1.22-2.4-2.84-2.87-4.64-.41-1.64.28-5.19,1.23-5.59Z"/>
        <path class="cls-84" d="m430.64,241.56s-6.55-.28-9.01.41-1.5,2.18-1.5,2.18l3.69,5.46,8.05-3.41c.79-1.64.27-3.61-1.23-4.64Z"/>
        <path class="cls-8" d="m447.7,244.83l-.95-3.02c3.41,0,6.14-4.5,8.19-5.04.68-.41,2.18-.96,1.62-1.5h-.95c-2.05.41-4.37.81-6.83,1.36l-18.96,10.09c.03.38.12.75.28,1.09.95,3.24,2.43,3.54,2.43,3.54,0,0,.68,2.43,6.54,1.78,3.04-2.59,5.92-5.36,8.63-8.3Z"/>
        <path class="cls-84" d="m440.05,241.42c2.48-3.14,5.22-6.06,8.19-8.73,1.78-1.5,6.14,1.09,6.14,1.09,0,0,3.24.96,2.73,1.5-.28.28-2.18.14-3.41,1.23s-3.41,3.41-6,4.05c-.55.14-4.09,3.24-4.51,3.4-2.18.59-3.14-2.55-3.14-2.55Z"/>
        <path class="cls-5" d="m442.24,237.6s-9.96,7.5-16.65,10.5c-1.09.55-2.04.81-3-1.77-3.54-10.23-10.23-30-15.01-29.88-4.09.14-5.59,10.09-3.24,17.74,2.73,8.46,6.55,18.01,9.96,23.74,4.78,7.91,9.55,6.41,11.59,5.19,12.28-6.96,19.51-17.02,21.56-19.65.51-.41-1.39-6.41-5.21-5.86Z"/>
        <path class="cls-15" d="m429.82,259.7c-2.04,1.09-6.82,2.72-11.59-5.19-3.55-5.86-8.19-19.1-10.92-27.56-1.36-4.51-1.62-9.41-.81-10.54-3.24,2.73-4.5,10.23-2.18,17.88,2.73,8.46,6.55,18.01,9.96,23.74,4.78,7.91,9.55,6.41,11.59,5.18,4.9-2.71,9.4-6.09,13.36-10.04-2.96,2.42-6.11,4.61-9.41,6.54Z"/>
        <path class="cls-63" d="m1125.62,318.02h-47.74c-.75-.06-1.41.51-1.47,1.26s.51,1.41,1.26,1.47c.07,0,.14,0,.21,0h20.87v43.48h3v-43.5h23.88c.75.06,1.41-.51,1.47-1.26s-.51-1.41-1.26-1.47c-.07,0-.14,0-.21,0v.02Z"/>
        <path class="cls-63" d="m1103.43,365.34h-6.28v4.5h6.28v-4.5Z"/>
        <path class="cls-63" d="m1119.49,415.3h-16.06v-44.23h-6.28v44.23h-16.1c-.61-.06-1.16.38-1.22.99,0,.03,0,.07,0,.1,0,.6.47,1.09,1.06,1.09.01,0,.02,0,.03,0h16.21v21.01h6.3v-20.91h16.09c.61.07,1.15-.37,1.22-.97,0-.04,0-.07,0-.11-.1-.64-.62-1.13-1.26-1.2Z"/>
        <path class="cls-63" d="m1097.11,439.83l-19.92,3.84h46.11l-19.88-3.84h-6.32Z"/>
        <path class="cls-82" d="m1123.99,318.02h-45.03c-4.13.09-7.55-3.18-7.64-7.31,0-.02,0-.03,0-.05v-2.05c.06-4.13,3.46-7.42,7.59-7.36.02,0,.03,0,.05,0h45.03c4.13-.09,7.55,3.19,7.64,7.33,0,.01,0,.02,0,.04v2.05c-.07,4.13-3.47,7.43-7.6,7.36-.01,0-.02,0-.04,0Z"/>
        <path class="cls-125" d="m1022.07,442.96l5.46,3.41,3.96-3.41-3.24-1.91-6.17,1.91Z"/>
        <path class="cls-6" d="m1028.76,422.78s-2.19,3.14-3.96,5.59c-2.18,3.24-16.64,6.14-26.88,6.55-2.32.14-2.73,2.32-3.24,4.36-.13.28,2.59,2.59,5.05,3.69,2.98,1.38,6.16,2.3,9.41,2.72,6.17.49,12.37-.2,18.28-2.04,0,0,1.09-.14.81.68-.14.41-.41,1.78-.54,2.04l10.77-.95c.86-1.41,1.64-2.87,2.33-4.37,1.62-4.79,2.76-9.72,3.4-14.73l-15.44-3.55Z"/>
        <path class="cls-129" d="m1069.82,272.15s-19.65,52.8-27.56,80.22c-2.73,9.28-1.36,11.46-3,18.15-6.01,24.01-14.46,55.94-14.74,56.48-3.4,6.41,15.96,7.09,19.79,5.59,0,0,12.16-34.24,18.82-56.75.96-3.24,2.43-14.19,3.55-16.21,28.11-55.66,33.43-74.22,33.43-74.22,0,0,2.43-12.83-8.86-17.6-7.34-3.2-15.9-1.47-21.42,4.34Z"/>
        <path class="cls-19" d="m1055.77,367.24c3.13-2.18-3-3.14-1.37-4.22s2.43-1.09.81-2.43,1.09-2.43,5.73-15.28c3.68-10.1,22.25-58.37,29.61-77.63.14,0,.28.14.41.14,11.35,4.78,8.88,17.6,8.88,17.6,0,0-5.33,18.56-33.43,74.22-1.09,2.04-2.59,13.09-3.55,16.21-6.55,22.51-18.82,56.75-18.82,56.75-3.23.68-6.55.82-9.83.41,4.51-14.6,18.95-63.99,21.56-65.76Z"/>
        <path class="cls-99" d="m994.64,439.28c.15-.68.41-1.22.55-1.91.28,1.23,1.91,2.33,4.91,3.69,4.29,1.79,8.86,2.85,13.5,3.14,4.37,0,12.83-1.36,14.33-1.36s1.09.95,1.09.95h2.18c.81,0,6.27-.54,8.59-.81l-1.37,2.43-10.77.96c.25-.66.43-1.35.54-2.04.28-.96-.81-.68-.81-.68-1.62.41-10.37,2.86-18.28,2.04-3.26-.43-6.43-1.35-9.41-2.73-1.92-.86-3.65-2.1-5.06-3.66Z"/>
        <path class="cls-2" d="m1083.06,396.17s7.64,9.83,8.59,13.64-3.81,12.83-3.81,12.83c0,0,3.96,2.43,4.78,2.43s12.16-11.59,12.16-11.59c0,0,5.59-4.05-2.18-9.42-3.55-2.43-8.73-12.97-8.73-12.97l-10.8,5.08Z"/>
        <path class="cls-120" d="m1098.48,420.98l5.32-2.18-.55-4.64-3.14,1.5-1.63,5.32Z"/>
        <path class="cls-6" d="m1098.34,409.13c-1.22-.91-2.71-1.39-4.23-1.36-2.86-.14-6,1.78-5.46,3,1.5,3.41-4.77,11.73-10.09,20.06-1.36,2.18,1.23,5.46,1.78,6.27,1.83-.02,3.63-.39,5.32-1.09,2.71-1.21,5.2-2.87,7.37-4.9,5.05-4.78,8.05-12.28,8.73-13.64,0,0,.41-.81.95-.14.28.27.81,1.22,1.1,1.5l5.73-6.41c.24-.41.29-.91.13-1.36-.41-1.09-1.62-3.24-1.91-3.96-2.72-4.91-4.5-5.19-5.05-5.46-1.35-.54-2.59-.13-2.43.14,1.62,3-1.92,7.37-1.92,7.37Z"/>
        <path class="cls-56" d="m1085.65,436.01c-1.68.71-3.49,1.08-5.32,1.09-.44-.57-.85-1.16-1.23-1.78,4.26-.5,8.28-2.21,11.6-4.91,4.37-3.54,9.55-11.73,10.23-12.97,1.09-1.91,3.41-3.41,4.91-5.05,1.02-1.1,1.81-2.4,2.32-3.82.41.96.96,1.91,1.23,2.59.23.44.17.98-.14,1.37l-5.73,6.41c-.28-.55-.65-1.06-1.09-1.5-.41-.68-.95.13-.95.13-.69,1.37-3.69,8.87-8.73,13.64-2.04,2.04-4.45,3.67-7.1,4.79Z"/>
        <path class="cls-129" d="m1099.29,267.24c-8.46,16.64-25.13,46.79-34.51,74.5-1.62,4.64-.81,8.87,1.09,16.78,4.23,17.73,20.07,47.89,20.07,47.89,1.91,2.72,1.35,3,7.9.68,2.59-.96,3.83-3.55,8.19-8.87-.96-1.23-11.88-41.48-14.74-46.53-1.09-1.78-1.35-2.32-.67-2.43s-1.37-1.22-1.09-1.77c.54-1.09.95-1.09,1.22-1.62,7.64-12.97,20.33-32.06,22.38-34.93,4.78-6.82,6.15-9.96,9.83-16.21,4.5-7.77-.55-18.28-.55-18.28l-19.1-9.2Z"/>
        <path class="cls-10" d="m1074.05,357.83c-.69-2.04-3.55-3.54-4.86-4.78-1.62-1.5-1.5-2.04-.81-2.04,3.54,0,4.78,0,6.69-3s2.32-6.69,6.96-13.1c3.82-5.46,10.54-13.1,19.64-26.61,6.97-10.06,11.17-21.77,12.16-33.97l4.64,2.18s5.05,10.54.55,18.28c-3.68,6.27-5.05,9.42-9.83,16.21-2.04,2.86-14.74,21.96-22.38,34.93-.52.45-.94,1-1.22,1.62-.28.54,1.62,1.62,1.09,1.78-.81.28-.41.68.67,2.43,2.86,5.04,13.78,45.29,14.74,46.52-4.23,5.19-5.46,7.91-8.19,8.87-8.67-17.12-17.67-43.45-19.85-49.32Z"/>
        <path class="cls-5" d="m1038.44,227.13s18.15,11.19,18.96,8.87c2.05-5.32,3.15-18.14,5.05-27.83,3.15-15.28,9.14-22.92,15.96-21.56,3.96.81,7.23-.96,7.52,6.14s-13.93,47.35-18.83,56.75c-1.71,3.66-5.95,5.38-9.73,3.96,0,0-16.91-6.27-24.69-16.21-1.33-2.06.44-11.89,5.76-10.11Z"/>
        <path class="cls-9" d="m1042.95,235.32s15.95,8.05,17.74,9.69c2.18,2.04,1.5-1.91,1.77-2.87.15-.41,2.43,2.19,2.59,1.78s-1.78-4.36-1.62-4.78c1.22-6.27,2.32-15.4,3.82-22.78,3.13-15.28,9.14-22.93,15.96-21.56.9.15,1.82.24,2.73.27-1.78,11.06-14.07,45.71-18.57,54.32-1.7,3.64-5.92,5.37-9.68,3.96,0,0-12.01-4.5-20.47-11.87.27-3.3,2.16-7.26,5.72-6.15Z"/>
        <path class="cls-5" d="m1121.8,217.85c4.15-2.28,5.66-7.49,3.37-11.64-.04-.08-.09-.15-.13-.23-.28-.41-.41-.68-.68-1.09-2.04-3.15-13.92-11.35-19.65-14.74-11.19-6.82-19.64-3.41-19.64-3.41-.69-.14-18.82,4.23-17.6,24.97,1.91,31.38,3.41,41.88,1.62,54.71-.28,2.19-2.43,7.78,3.95,9.01,5.19,1.09,4.64.54,16.21,5.87,9.54,4.36,25.51-.41,27.01-3.14s-1.5-14.06,0-25.79c1.86-11.87,5.54-34.52,5.54-34.52Z"/>
        <path class="cls-13" d="m1126.3,210.62c-.13,4.64-4.5,7.23-4.5,7.23,0,0-3.68,22.65-5.18,34.38s1.5,23.19,0,25.78c-1.23,2.19-11.88,5.19-21.15,4.37,4.78-.81,8.46-3,9.01-4.09,2.04-4.09.81-18.01,2.04-28.79,1.91-18.01,11.46-38.34,12.16-38.2,2.45-.84,5.06-1.07,7.62-.68Z"/>
        <path class="cls-2" d="m1101.61,169.14c.41,2.18,3.96,21.96,3.96,21.96-2.06,4.52-5.77,8.09-10.37,9.96-6.41,2.32-7.09-5.32-7.09-5.32l-3.55-19.24c-.15,0,16.23-11.19,17.05-7.37Z"/>
        <path class="cls-2" d="m1114.44,286.34c-.56-.11-1.13,0-1.62.28-1.5,1.09-5.32,3.54-6.68,5.46-.68.86-.54,2.12.33,2.8.03.02.05.04.08.06l5.73,4.23c.88.67,2.13.55,2.87-.28v-.14c1.5-2.18,3.95-5.86,4.05-8.18-.13-2.55-3.13-3.82-4.76-4.23Z"/>
        <path class="cls-5" d="m1107.34,290.71c-2.86-1.62,14.74-21.69,15.4-32.61,0-.68-.68-1.22-.68-1.77-.14-.96.28-.69.13-1.62,0-.28-1.36-4.51-1.36-4.78-1.84-7.29-4.17-14.44-6.96-21.42-5.46-14.46.81-25.38,6.82-24.83,5.33.54,8.05,7.77,11.05,18.28,2.59,9.14,8.05,29.47,7.38,36.97-1.37,14.88-11.2,25.25-22.25,38.1-.81.95-2.43,1.22-4.91-.81-1.79-1.61-3.35-3.47-4.62-5.51Z"/>
        <path class="cls-13" d="m1116.07,297.53c-.13,0-.27.14-.41.14h-.54c-1.1-.28-2.12-.79-3-1.5.83.41,1.84.11,2.32-.68,5.59-8.87,20.6-33.7,18.55-36.43-.27-.41-5.05.14-5.05-.54,0-.41,4.23-.41,5.46-2.59.27-.41-2.05,0-4.05,0s-1.62-1.62-1.1-1.91c1.91-.81,5.6-2.19,5.2-5.33-1.37-11.35-4.37-29.47-8.2-39.72-1.77-4.78-6.14-5.32-4.05-5.32.19-.02.39.03.55.14,4.64,1.22,7.23,8.32,10.09,18.14,2.59,9.14,8.05,29.47,7.38,36.97-1.37,14.88-11.2,25.25-22.25,38.1-.19.31-.52.51-.89.54Z"/>
        <path class="cls-32" d="m1024.25,207.76l.96-.68.68.41-1.09.95-.55-.68Z"/>
        <path class="cls-127" d="m1036.53,239c.14.68.14.81.96,1.09l20.6,5.46c.68,0,1.09-.28,1.09-.96l-10.23-30.43c-.14-.72-.67-1.3-1.37-1.5l-21.69-5.72c-.54-.14-.95.13-.81.81l11.46,31.24Z"/>
        <path class="cls-127" d="m1057.27,245.68l.96-.81.67.41-.81.81-.82-.41Z"/>
        <path class="cls-89" d="m1035.71,239.68c.14.69.14.81.96,1.09l20.6,5.46c.68,0,1.09-.27,1.09-.96l-10.23-30.56c-.23-.64-.73-1.14-1.37-1.37l-21.69-5.72c-.54-.14-1.09.27-.81.81l11.46,31.24Z"/>
        <path class="cls-2" d="m1027.8,226.17c-.11.4-.16.82-.14,1.23.14.68.41.81.69,1.5.13.28,0,1.5.13,1.78.55.81.96.95,1.09,1.22s0,1.23.69,1.62c.43.24.8.57,1.09.96.44.84,1.42,1.24,2.32.96,1.2.03,2.4-.16,3.54-.55.56-.27.8-.93.55-1.5-.15-.14-.28-.41-.41-.41-.61-.24-1.26-.33-1.91-.28-.27,0,2.33.28,2.33-1.36,0-.54-.31-1.03-.81-1.23-.95-.11-1.9-.16-2.85-.14,0,0-.41-.13.13-.13.97,0,1.94-.14,2.87-.41.53-.51.54-1.35.03-1.88,0,0-.02-.02-.03-.03-.15-.14-.28-.14-.28-.28-1.57-.32-3.18-.37-4.77-.14.96-.39,1.97-.66,3-.81.68-.14.81-1.78-.41-2.04-.28-.13-5.07-.67-6.83,1.91Z"/>
        <path class="cls-13" d="m1087.02,212.53l-.41-8.32,3.24-2.59,1.62.28,1.23,3.96-3.24,6c-.41.62-.68,1.32-.81,2.05,0,0,2.32,26.87,2.6,55.53,0,1.22-4.23,9.14-4.23,9.14,0,0-3.96-10.23-3.82-11.87,3.4-39.56,3.12-48.57,3.81-54.16Z"/>
        <path class="cls-13" d="m1090.56,201.21c.4.42.4,1.08,0,1.5l-5.73,4.91c-.54.41-.95.81-.81-.54l1.09-12.01c.54-5.19,2.73-5.59,2.73-5.59-.09.95-.14,1.91-.14,2.86-.02,3.19.99,6.29,2.86,8.87Z"/>
        <path class="cls-13" d="m1105.16,187.83c.15,1.73-.28,3.46-1.23,4.91-3,4.23-12.68,8.59-12.68,8.59-.69.41-.69.55-.55,1.91l.81,7.09c0,1.62,1.09.96,1.78.28,0,0,10.23-7.23,12.16-9.14,4.78-4.36,4.37-7.09,3.55-8.59-.57-.82-3.83-5.05-3.83-5.05Z"/>
        <path class="cls-63" d="m1086.61,211.58l-.41-8.33,3.24-2.59,1.62.27,1.22,3.96-3.24,6.01c-.41.62-.69,1.31-.81,2.04,0,0,.96,26.87,1.22,55.53,0,1.22-4.05,11.35-4.05,11.35,0,0-3.82-12.41-3.68-14.19,3.38-39.46,4.21-48.46,4.89-54.06Z"/>
        <path class="cls-39" d="m1090.15,200.25c.41.41.41,1.09,0,1.5l-5.73,4.91c-.54.41-.95.81-.81-.54l1.09-12.01c.54-5.19,2.73-5.59,2.73-5.59-.1.95-.15,1.91-.15,2.86-.01,3.19.99,6.29,2.86,8.87Z"/>
        <path class="cls-39" d="m1104.75,186.88c.15,1.73-.28,3.46-1.22,4.91-3,4.22-12.69,8.59-12.69,8.59-.69.41-.69.54-.54,1.91l.81,7.1c0,1.62,1.09.95,1.77.27,0,0,10.23-7.23,12.16-9.14,4.78-4.37,4.37-7.09,3.55-8.6-.55-.82-3.83-5.04-3.83-5.04Z"/>
        <path class="cls-11" d="m1103.43,178.97c-.55.68-6.96,9.41-8.06,10.77-3.96,4.78-7.09,2.05-7.77.28-.28-5.19-.41-6.27-.41-6.27l15.7-7.77s.11.81.55,3Z"/>
        <path d="m1101.61,176.65c12.16-.69,26.75-33.7,2.19-41.21-29.33-8.87-31.52,31.52-21.97,36.56,5.05,2.59,14.87,5.05,19.78,4.65Z"/>
        <path class="cls-79" d="m1087.97,190.56c.58.14,1.19.14,1.78,0,.83-.31,1.65-.67,2.43-1.09,1.42-.97,2.66-2.17,3.68-3.55.68-.77,1.32-1.59,1.91-2.43.81-1.22,1.5-2.43,2.19-3.68s1.09-2.05,1.62-3.14c.68-1.4,1.23-2.86,1.62-4.37.56-1.55.97-3.15,1.23-4.78.27-1.36.41-2.73.54-4.05.16-.86.25-1.72.28-2.59,0-.68-.14-1.22-.14-1.91-.03-.6-.12-1.19-.27-1.78l-.41-1.22c-.33-.9-.89-1.7-1.62-2.32-.99-.99-2.29-1.61-3.68-1.78-1.58-.23-3.18-.32-4.78-.28l-6.38,38.95Z"/>
        <path class="cls-67" d="m1083.74,185.66c.14.27.28.68.41.95l.41.81c.51,1.22,1.43,2.23,2.59,2.87.25.14.53.23.81.27l7.1-37.52c.27-.81-.28-1.5-.41-1.36-1.09.12-2.17.3-3.24.54-1.25.25-2.45.71-3.54,1.37-2.52,1.45-4.27,3.95-4.78,6.82-.28,1.91-.68,3.69-.96,5.59-.14,1.5-.28,3.14-.28,4.64v3.24c0,.81.14,1.62.14,2.43s.14,1.36.14,2.05c-.02.62.05,1.23.21,1.83.13.55.13.96.27,1.5,0,.28.14.55.14.81.45.93.58,2.06.99,3.15Z"/>
        <path class="cls-18" d="m1243.34,435.15c15.54,0,28.15-1.8,28.15-4.05s-12.61-4.05-28.15-4.05-28.16,1.8-28.16,4.05,12.61,4.05,28.16,4.05Z"/>
        <path class="cls-97" d="m1272.93,253.16c2.25-.96,1.21,1.21,1.21,1.21-36.59,49.62-34.75,146.54-34.75,146.54-3.53-95.64,33.54-147.74,33.54-147.74Z"/>
        <path class="cls-97" d="m1242.21,301.24c1.21-3.62-.96-2.49-.96-2.49-9.17,17.21-1.86,102.15-1.86,102.15-4.82-92.75,2.74-99.66,2.82-99.66Z"/>
        <path class="cls-97" d="m1227.42,278.8c.88-3.06,2.01-1.28,2.01-1.28-4.75,30.08,14.31,120.96,14.31,120.96-12.62-31.45-16.32-119.68-16.32-119.68Z"/>
        <path class="cls-75" d="m1229.58,426.95v-43.92c0-1.71,1.36-3.1,3.06-3.14h13.1c1.79-.03,3.27,1.39,3.3,3.19,0,.04,0,.08,0,.11v43.84c-.03,2.52-2.06,4.55-4.58,4.58h-10.22c-2.55-.06-4.6-2.11-4.66-4.66Z"/>
        <path class="cls-29" d="m1245.75,379.26v.24c.02.2-.12.37-.31.4,0,0,0,0,0,0h-11.98c-.2.02-.38-.12-.41-.32v-.32c0-1.79,1.45-3.24,3.24-3.24h6.27c1.77.03,3.19,1.47,3.19,3.24Z"/>
        <path class="cls-44" d="m1265.57,265.77l-1.69-1.21c-13.68.24-6.2-27.42-6.2-27.42,14.4,19.14,7.16,26.86,7.16,26.86l1.05,1.29-.32.48Z"/>
        <path class="cls-123" d="m1265.57,265.85l-1.69-1.21c-5.55-7.3-5.72-20.19-5.72-20.19.81-1.69.96,0,.96,0-1.29,9.25,5.67,19.55,5.67,19.55l1.05,1.29-.29.56Z"/>
        <path class="cls-50" d="m1263.68,256.77l-.32-3.06c-13.67-14.48,23.4-33.54,23.4-33.54-6.49,34.34-21.89,34.05-21.89,34.05l-.4,2.33-.79.22Z"/>
        <path class="cls-85" d="m1263.68,256.85l-.32-3.06c2.43-13.19,16.08-25.89,16.08-25.89,2.5-.81.88,1.05.88,1.05-11.18,7.64-15.4,25.25-15.4,25.25l-.4,2.33-.85.32Z"/>
        <path class="cls-106" d="m1255.84,288.13l-2.49-1.62c-20.19.24-9.17-35.47-9.17-35.47,21.23,24.77,10.54,34.67,10.54,34.67l1.53,1.69-.41.74Z"/>
        <path class="cls-122" d="m1255.84,288.02l-2.49-1.62c-8.2-9.49-8.45-26.06-8.45-26.06,1.05-2.08,1.37.08,1.37.08-1.85,11.91,8.44,25.25,8.44,25.25l1.53,1.69-.41.66Z"/>
        <path class="cls-102" d="m1248.54,312.34l-1.69-1.21c-13.67.24-6.19-27.42-6.19-27.42,14.39,19.14,7.15,26.86,7.15,26.86l1.05,1.29-.32.48Z"/>
        <path class="cls-87" d="m1248.54,312.42l-1.69-1.21c-5.55-7.3-5.67-20.19-5.67-20.19.81-1.69.96,0,.96,0-1.29,9.25,5.67,19.55,5.67,19.55l1.05,1.29-.32.56Z"/>
        <path class="cls-135" d="m1239.07,309.53l-1.69-1.21c-13.67.24-6.19-27.43-6.19-27.43,14.4,19.14,7.16,26.87,7.16,26.87l1.04,1.28-.32.49Z"/>
        <path class="cls-112" d="m1239.07,309.61l-1.69-1.21c-5.55-7.3-5.67-20.19-5.67-20.19.81-1.69.97,0,.97,0-1.3,9.25,5.67,19.55,5.67,19.55l1.04,1.28-.32.57Z"/>
        <path class="cls-47" d="m1228.53,298.43l-1.93-.81c-13.19,3.53-12.62-25.13-12.62-25.13,18.65,15.05,13.43,24.32,13.43,24.32l1.28,1.05-.16.58Z"/>
        <path class="cls-116" d="m1228.53,298.43l-1.93-.81c-7.23-5.8-10.45-18.18-10.45-18.18.4-1.69.96-.16.96-.16,1.05,9.17,10.3,17.53,10.3,17.53l1.28,1.05-.16.58Z"/>
        <path class="cls-117" d="m1230.46,321.11l-1.93-.81c-13.18,3.54-12.62-25.09-12.62-25.09,18.65,15.04,13.43,24.32,13.43,24.32l1.29,1.05-.16.54Z"/>
        <path class="cls-76" d="m1230.46,321.11l-1.93-.81c-7.24-5.79-10.46-18.18-10.46-18.18.41-1.69.97-.16.97-.16,1.04,9.17,10.29,17.54,10.29,17.54l1.29,1.05-.16.57Z"/>
        <path class="cls-59" d="m1232.08,337.92l-2.98-1.05c-21.08,5.55-18.82-34.58-18.82-34.58,28.72,20.27,20.03,33.38,20.03,33.38l2.01,1.37-.24.88Z"/>
        <path class="cls-114" d="m1232.08,337.84l-2.98-1.05c-11.1-7.72-15.61-24.93-15.61-24.93.56-2.5,1.45-.32,1.45-.32,1.12,12.87,15.4,24.13,15.4,24.13l2.01,1.37-.28.8Z"/>
        <path class="cls-95" d="m1236.42,365.74l-1.94-.81c-13.19,3.54-12.62-25.09-12.62-25.09,18.65,15.04,13.43,24.32,13.43,24.32l1.28,1.05-.15.54Z"/>
        <path class="cls-97" d="m1236.42,365.74l-1.94-.81c-7.23-5.79-10.45-18.18-10.45-18.18.4-1.69.96-.16.96-.16,1.05,9.17,10.3,17.54,10.3,17.54l1.28,1.05-.15.57Z"/>
        <path class="cls-57" d="m1239.88,368.64l.32-2.09c-6.67-11.9,21.23-18.41,21.23-18.41-10.05,21.71-20.27,18.98-20.27,18.98l-.65,1.52h-.64Z"/>
        <path class="cls-97" d="m1239.88,368.56l.32-2.09c3.78-8.37,15.04-14.59,15.04-14.59,1.77-.08.4.88.4.88-8.68,3.3-14.47,14.32-14.47,14.32l-.65,1.53-.64-.05Z"/>
        <path class="cls-49" d="m1228.06,279.29l-.73-1.93c-11.5-7.48,10.22-26.22,10.22-26.22,1.2,23.88-9.1,26.22-9.1,26.22l.08,1.62-.48.31Z"/>
        <path class="cls-71" d="m1228.06,279.29l-.73-1.93c-.48-9.25,6.6-19.95,6.6-19.95,1.53-.88.73.57.73.57-6.2,6.92-6.2,19.38-6.2,19.38l.08,1.62-.48.31Z"/>
        <path class="cls-115" d="m1244.22,333.9l-1.21-.88c-9.73.16-4.43-19.63-4.43-19.63,10.3,13.68,5.15,19.14,5.15,19.14l.73.88-.24.49Z"/>
        <path class="cls-64" d="m1244.3,333.9l-1.21-.88c-4.05-5.23-4.1-14.4-4.1-14.4.48-1.21.64,0,.64,0-.88,6.49,4.1,13.91,4.1,13.91l.73.88-.16.49Z"/>
        <path class="cls-46" d="m1240.92,356.42l-1.21-.89c-9.73.16-4.42-19.62-4.42-19.62,10.3,13.67,5.15,19.14,5.15,19.14l.72.88-.24.49Z"/>
        <path class="cls-97" d="m1241,356.42l-1.21-.89c-4.05-5.22-4.1-14.39-4.1-14.39.49-1.21.65,0,.65,0-.88,6.49,4.1,13.91,4.1,13.91l.72.88-.16.49Z"/>
        <path class="cls-80" d="m1242.93,345.96l1.37-.65c9.97,2.01,8.2-18.41,8.2-18.41-13.04,11.41-8.77,17.84-8.77,17.84l-.88.81.08.41Z"/>
        <path class="cls-97" d="m1243.01,345.96l1.37-.64c4.99-4.35,6.83-13.35,6.83-13.35-.32-1.21-.72-.08-.72-.08-.32,6.59-6.76,12.87-6.76,12.87l-.88.81.16.4Z"/>
        <path class="cls-107" d="m1246.92,322.96l2.66-.49c17.69,6.92,18.74-25.33,18.74-25.33-26.14,12.71-19.71,24.21-19.71,24.21l-1.85.81.16.8Z"/>
        <path class="cls-37" d="m1246.92,322.88l2.66-.49c10.13-4.82,15.27-18.01,15.27-18.01-.32-2.01-1.21-.41-1.21-.41-1.92,10.13-15.04,17.37-15.04,17.37l-1.85.81.16.72Z"/>
        <path class="cls-130" d="m1252.74,299.37l1.53-.08c8.6,5.31,13.99-14.48,13.99-14.48-16.17,6.27-14.4,13.78-14.4,13.78l-1.13.4v.38Z"/>
        <path class="cls-92" d="m1252.74,299.37l1.53-.08c6.19-2.43,11.02-10.21,11.02-10.21.08-1.29-.64-.32-.64-.32-2.58,6.11-10.78,9.82-10.78,9.82l-1.13.4v.41Z"/>
        <path class="cls-132" d="m1261.51,276.47l2.16.08c13.36,8.22,17.94-16.1,17.94-16.1-22.36,5.3-18.58,15.12-18.58,15.12l-1.52.32v.58Z"/>
        <path class="cls-131" d="m1261.51,276.56l2.16.08c8.61-2.02,14.33-11.18,14.33-11.18-.08-1.62-.96-.57-.96-.57-2.74,7.4-14,10.7-14,10.7l-1.52.32v.65Z"/>
        <path class="cls-86" d="m893.87,253.97s4.31,7.03,3.7,11.81c-.28,2.22-2.51,8.61-2.51,8.61,0,0-.81,1.38-1.83,1.38-.96-.23-1.87-.64-2.69-1.19-1.78-1.16-1.41-5.63-1.41-5.63.33-1.87.4-3.78.24-5.67-.21-1.63-.24-3.28-.08-4.92l4.6-4.39Z"/>
        <path class="cls-26" d="m893.79,254.97s3.88,6.49,3.91,8.5-2.57,7.24-2.57,7.24c0,0-1.15.76-1.46-.57-.38-1.62.49-4.27.49-4.27,0,0,0-.19-1.5-1.83s-.99-4.81-.99-4.81l2.12-4.26Z"/>
        <path class="cls-111" d="m880.93,168.99s8.92-.31,10.54,12.52-.2,37.02,1.2,42.63c2.21,8.86,5.01,34.86,5.01,34.86,0,0-4.41,3.81-7.82,2.21-1.27-.6-8.55-21.99-10.16-38.55-1.61-16.56-2.81-54.03,1.22-53.66Z"/>
        <path class="cls-119" d="m867.29,246.59s-8.11-3.16-2.81,28.16c1.87,11.08,8.22,50.46,8.22,50.46,0,0-4.3,15.1-1.8,29.14,2,11.22,6.65,35.44,5.87,36.38-3.47,4.18,2.75,11.35,2.75,11.35,0,0,2.18,8.5,6.07,11.29,1.49,1.07,7.35.41,5.05-3.24-1.5-2.43-4.81-10.62-5.31-18.84-.25-4,.4-41.89,1.2-60.32.13-3.03,1.39-3.11,1.62-10.62.98-34.35-.81-75.31-.81-75.31l-20.05,1.56Z"/>
        <path class="cls-16" d="m879.99,391.69c-1.62-10.16-6.69-29.4-4.82-54.78.57-7.73,7.48-15.23,6.68-20.04-1.47-8.84,3.24-19.27,3.24-19.27,0,0-9.73-1.87-17.64-3.28,2.43,12.8,5.28,30.89,5.28,30.89,0,0-4.3,15.1-1.8,29.14,2,11.22,6.65,35.44,5.87,36.38-3.47,4.18,2.75,11.35,2.75,11.35,0,0,2.18,8.5,6.07,11.29.63.35,1.35.5,2.07.45-2-7.21-6.49-14.41-7.7-22.13Z"/>
        <path class="cls-72" d="m885.43,223.97s-8.33,6.13-16.3,25.75c-.16,8.35.24,16.69,1.21,24.99,2.2,20,2.89,40.54,1.85,46.79-1.46,8.77-3.92,15.86-2.43,32.32,1.13,12.26,4.1,32.53,4.16,33.71,1.62.47,2.21,1.39,6.92,1.39,1.85,0,4.79-.81,5.14-1.68.24-9.34.09-31.5,2.02-51.66.26-2.66,1.48-7.21,1.85-12.47,3.04-42.66,3.75-66.22.46-81.63-1.29-5.93-2.91-11.77-4.88-17.51Z"/>
        <path class="cls-14" d="m869.13,249.74c-.16,8.35.24,16.69,1.21,24.99,2.2,20,2.89,40.54,1.85,46.79-1.46,8.77-3.92,15.86-2.43,32.32,1.13,12.26,4.1,32.53,4.16,33.71,1.21.35,1.85.94,4.05,1.22-.81-7.9-2.77-41.85-1.71-49.45,1.15-8.32,2.54-6.75,2.54-13.86-.88-6.32.81-50.69-2.31-57.27-4.51-9.51-1.15-22.38,1.56-35.1-3.59,5.19-6.58,10.77-8.91,16.64Z"/>
        <path class="cls-63" d="m857.78,402.49s-2.74.77-1.49,6.64c1.49,7.07,2.17,14.53,2.28,15.17.2,1.2,2.43,4.28,6.64,6.49,1.01.39,2.13.36,3.11-.08,1.62-.98,1.4-5.34.81-7.3-2.43-8.57-5.32-17.74-5.32-17.74,0,0-3.46-5.54-6.03-3.18Z"/>
        <path class="cls-86" d="m849.85,259.21c-.7,9.54-.29,19.13,1.21,28.58,3.4,20.11,6.3,35.1,3.4,43.51-2.89,8.41-4.01,19.17-1.62,34.67,1.88,12.16,3.31,22.16,4.21,27.66.54,3.35.57,6.76.1,10.13,0,0,1.56,10.37,2.43,14.43,1.62,7.61,9.25,6.27,8.42,1.9-.81-4.21-2.3-15.23-2.51-19.46-.21-4.4,1.62-36.08,3.81-59.12.25-2.66,1.29-5.2,2.09-10.42,1.62-10.78,4.52-84.41,4.52-84.41,0,0-18.08-7.09-26.06,12.53Z"/>
        <path class="cls-16" d="m861.02,407.46c.81-9.89-4.74-34.2-5.08-51.31-.27-13.9,1.62-16.3,4.27-24.86,1.69-5.83,2.68-11.84,2.94-17.9,0,0-3.81-.74-8.59-1.62,1.29,8.41,1.62,14.49-.09,19.54-2.9,8.42-4.01,19.17-1.62,34.67,1.88,12.16,3.31,22.16,4.21,27.66.54,3.35.57,6.76.1,10.13,0,0,1.56,10.37,2.43,14.43.28,1.87,1.29,3.56,2.81,4.69-.99-5.12-1.77-10.98-1.39-15.43Z"/>
        <path class="cls-72" d="m878.71,231.12s-12.79-8.38-18.87-1.04c-5.03,6.06-10.04,17.84-9.99,29.13-.19,9.54.22,19.09,1.21,28.58,2.2,19.99,3.58,37.48,2.55,43.71-1.47,8.78-2.89,18.01-1.39,34.46,1.12,12.26,3.4,30.52,3.46,31.69,1.62.47,2.63,2.21,7.3,1.39,2.94-.52,3.24-1.81,3.81-1.91.24-9.34,1.53-32.14,3.46-52.3.26-2.66,1.73-7.51,2.31-15.01.81-10.87,6.15-98.71,6.15-98.71Z"/>
        <path class="cls-14" d="m860.35,399.17c-.71-3.66-3.74-42.63-2.59-50.86s3.12-3.81,3.12-10.91c-.88-6.32-1.33-34.93-2.89-53.05-1.31-15.27.23-44.7,7.44-57.2l-.42.06c-2.03.28-3.87,1.31-5.17,2.89-5.03,6.06-10.04,17.84-9.99,29.13-.19,9.54.22,19.09,1.21,28.58,2.2,19.99,3.58,37.48,2.55,43.71-1.47,8.78-2.89,18.01-1.39,34.46,1.12,12.26,3.4,30.52,3.46,31.69.12.03.24.07.35.12,1.22.5,2.21,1.38,4.32,1.39Z"/>
        <path class="cls-86" d="m842.26,236.94s8.85,9.36,9.69,8.74c2.43-1.82,6.49-6.08,7.71-6.08,1.8.19,3.59.55,5.33,1.06,0,0,2.8,2.31,1.2,2.91s-6.49,4.21-9.53,7.01-4.86,3.01-6.49,1.01c-.59-.74-13.92-10.54-13.86-11.29.07-1.27,5.95-3.36,5.95-3.36Z"/>
        <path class="cls-63" d="m858.4,219.33c.71,2.7.49,7.71-1.62,12.66-.33,1.09-1.01,1.52,1.54,3.49,3.6,2.78,9.73,5.34,18.89,3.1,4.79-1.18,7.3-1.66,9.87-3.47,1.68-1.35,2.17-1.89,1.77-3.7-1.09-5.01-1.76-10.1-2.02-15.23,0-3.62.61-6.06,2.43-8.27,3.63-4.44,5.41-6.81,2.6-15.63-.81-2.48-2.19-8.44-3-12.43-1.62-7.82-5.08-10.72-8.02-10.82-12.43-.43-22.05,1.79-34.28,10.28-9.14,6.34.2,17.38.2,17.38,0,0,9.72,15.44,11.63,22.65Z"/>
        <path class="cls-2" d="m862.56,153.44c-.34,1.95-3.54,19.58-3.54,19.58,1.81,4.08,5.19,7.26,9.39,8.8,5.72,2.07,6.4-4.69,6.4-4.69l3.24-17.2c-.27,0-14.92-10.04-15.49-6.49Z"/>
        <path class="cls-24" d="m859.4,205.5c-6.87-17.93-7.3-14.59-8.05-15.59-1.35-1.18-4.28-.66-4.38-.58-2.43,1.79-2.35,3.75-1.72,5.2.46.75.97,1.47,1.52,2.15,0,0,9.73,15.4,11.63,22.65.71,2.7.49,7.71-1.62,12.66-.33,1.09-1.01,1.52,1.54,3.49,3.28,2.59,7.33,4.01,11.5,4.05-2.1-.59-4.05-1.87-3.24-5.22,1.62-7.45-.96-18.22,2.04-18.82,3.26-.73,6.12-2.67,8.02-5.42,0,0-13.64,4.85-17.24-4.57Z"/>
        <path class="cls-5" d="m836.12,226.26c-.37-.5.22-1.14.09-1.62s-.71-.56-.62-1c.06-.28.88-.49,1.04-.81.25-.45-.16-1.01.14-1.53,5.79-9.92,12.15-19.5,19.04-28.69,3.4-4.42-1.95-16.21-6.21-14.59-3.56,1.33-6.02,2.94-12.83,15.23-5.09,9.27-9.22,19.02-12.34,29.12,0,0-1.62,4.29,2.76,9.44,6.32,7.48,18.87,18.57,18.87,18.57,1.76-.03,3.37-.97,4.28-2.47,1.26-2.18.93-3.24.93-3.24,0,0-8.83-9.99-15.14-18.39Z"/>
        <path class="cls-24" d="m847.13,178.96c-2.12,1.35-3.75,2.78-10.37,14.31-5.3,9.39-9.52,19.34-12.59,29.67,0,0-1.34,3.74,3.01,8.92,6.32,7.48,18.87,18.57,18.87,18.57,1.47-.05,2.86-.71,3.81-1.83-4.18-2.62-14.96-13.86-18.47-19.54-2.43-3.94-1.4-7.42.2-11.23,2.61-6.19,11.2-25.51,12.97-28.33,2.93-4.65,3.98-6.76,5.73-7.22,2.79-2.4,5.71-4.63,8.76-6.7,2.17-1.29-2.09-2.28-2.29-2.18-3.33,1.63-6.55,3.49-9.62,5.57Z"/>
        <path class="cls-24" d="m871.47,182.16c-.2.37.18.81.67,1.43l4.67,5.23c.47.37.92.21.86-1.08v-10.31c-.23-2.43-1.26-4.18-1.88-6.41-.17-.66-.71-1.15-1.38-1.26-.02,1.14-.1,2.27-.24,3.4-.18,1.16-1.36,6.53-2.71,8.99Z"/>
        <path class="cls-24" d="m859.09,169.34c-.14,1.64.26,3.27,1.14,4.66,2.68,4,11.19,8.11,11.19,8.11.62.35.86.5.74,1.73l-1.41,7.54c-.15.59-.62.74-1.08.3,0,0-10.54-9.85-12.16-11.75-1.84-2.19-2.51-3.87-2.08-5.55.29-1.03,3.66-5.03,3.66-5.03Z"/>
        <path class="cls-5" d="m871.46,180.95c-.19.38.18.81.64,1.44l4.4,5.25c.45.38.86.21.81-1.07l-.08-10.31c-.23-2.43-1.19-4.18-1.79-6.42-.14-.64-.65-1.14-1.3-1.26,0,0-.08,1.91-.28,3.39-.15,1.16-1.15,6.53-2.41,8.98Z"/>
        <path class="cls-5" d="m859,168.15s.81,0,.81.38-.59,2.03-.34,2.92c.19.69.54,1.33,1.01,1.86,3.06,3.11,10.54,7.6,10.54,7.6.6.35.81.49.71,1.72l-1.37,7.55c-.15.58-.6.73-1.05.29,0,0-10.59-9.95-12.16-11.84-1.8-2.19-2.26-3.32-1.85-5.01.33-1.02,3.69-5.47,3.69-5.47Z"/>
        <path class="cls-63" d="m876.53,390.73s-3.38,2.21-1.13,8.67c.45,1.27.95,11.5.95,11.5h.81s-.55-11.12-.18-10.69c.4.55.68,1.18.81,1.84,1.12,3.53,2.72,7.96,3.62,10,.8,2.01,2.49,3.53,4.56,4.11,4.71,1.01,7.22,1.4,8.02,1.21,1.24-.32,1.35-.98,0-2.91-1.29-1.87-2.68-3.67-4.16-5.4,0,0,1.55,1.83-1.06,2.38-2.78.58-4.22-.68-7.66-8.58-1.77-3.95-3.3-8.01-4.58-12.14Z"/>
        <path class="cls-11" d="m860.96,162.13c.45.68,6.06,8.46,7.09,9.62,3.55,4.35,6.41,1.83,6.99.23.23-4.69.34-5.61.34-5.61l-13.86-7.09c-.07.97-.26,1.94-.57,2.86Z"/>
        <path d="m862.44,160.18c-10.88-.68-23.92-30.22-2.06-36.86,26.22-8.01,28.28,28.28,19.69,32.74-4.46,2.17-13.17,4.35-17.63,4.12Z"/>
        <path class="cls-79" d="m874.58,172.55c-.53.12-1.09.12-1.62,0-.77-.23-1.51-.58-2.17-1.04-1.22-.95-2.33-2.02-3.32-3.2-.57-.69-1.14-1.37-1.71-2.17-.69-1.03-1.38-2.18-1.95-3.24-.57-.92-1.07-1.87-1.49-2.86-.58-1.26-1.03-2.63-1.49-3.89s-.81-2.86-1.14-4.23-.34-2.43-.46-3.66c-.13-.76-.2-1.53-.22-2.29,0-.57.11-1.14.11-1.71.01-.55.09-1.09.23-1.62.11-.34.23-.81.34-1.14.32-.8.83-1.5,1.49-2.06.91-.81,2.01-1.37,3.2-1.62,1.4-.19,2.82-.26,4.24-.23l5.97,34.98Z"/>
        <path class="cls-67" d="m878.35,168.2c-.11.23-.22.58-.34.81l-.33.68c-.5,1.08-1.3,1.99-2.29,2.63-.21.13-.45.21-.69.24l-6.37-33.67c-.21-.47,0-1.03.45-1.26.96.1,1.92.25,2.86.45,1.14.24,2.24.67,3.24,1.26,1.11.65,2.08,1.51,2.87,2.52.77,1.05,1.28,2.27,1.48,3.55.34,1.72.57,3.32.81,5.03.12,1.38.24,2.75.24,4.13v2.98c0,.68-.11,1.48-.11,2.17s-.12,1.14-.12,1.83c-.13.53-.2,1.07-.23,1.62-.11.45-.11.92-.23,1.37,0,.23-.11.46-.11.69-.54,1.01-.66,1.93-1.13,2.96Z"/>
        <path class="cls-58" d="m581.94,281.03h-8.59v169.72h8.59v-169.72Z"/>
        <path class="cls-70" d="m924.52,281.03h-8.59v169.72h8.59v-169.72Z"/>
        <path class="cls-63" d="m543.46,270.79h427.58c3.96,0,7.09,1.51,7.09,3.24v1.91c0,1.78-3.14,3.24-7.09,3.24h-427.58c-3.96,0-7.09-1.5-7.09-3.24v-1.91c0-1.87,3.14-3.24,7.09-3.24Z"/>
      </g>
      
    </g>
  </g>
</svg>