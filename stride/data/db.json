{"jobs": [{"id": "1", "company": " Face Lift Company", "logo": "/assets/images/faceit.svg", "new": true, "featured": false, "position": "Full Stack Engineer", "role": "Frontend", "level": "Senior", "postedAt": "2d ago", "contract": "Part Time", "location": "Nigeria", "languages": ["JavaScript"], "description": "We are seeking a talented Front-End Developer to join our development team. This role involves translating UI/UX designs into functional code, optimizing web applications for maximum speed and scalability, and ensuring our products deliver an exceptional user experience. The ideal candidate is skilled in front-end web technologies, has a keen eye for design, and is passionate about delivering high-quality digital products.", "requirements": ["Develop new features for users ", "ensuring consistency and cross-platform responsiveness.", "Translate UI/UX wireframes and designs into high-quality,", "Collaborate with designers, other developers,", "and other team members to integrate elements smoothly.", "Implement best practices for web accessibility.", "cross-browser compatibility, and user experience."], "salary": "₦800,000 - ₦1,000,000", "tools": [], "slots": 2, "category": "Finance"}, {"id": "2", "company": "Short Hop", "logo": "/assets/images/shortly.svg", "new": true, "featured": true, "position": "Fullstack Developer", "role": "Fullstack", "level": "Mid Level", "postedAt": "1d ago", "contract": "Part Time", "location": "<PERSON>ja", "languages": ["Python"], "description": "We are seeking a talented <PERSON><PERSON><PERSON> to join our development team. This role involves translating UI/UX designs into functional code, optimizing web applications for maximum speed and scalability, and ensuring our products deliver an exceptional user experience. The ideal candidate is skilled in front-end web technologies, has a keen eye for design, and is passionate about delivering high-quality digital products.", "requirements": ["Develop new features for users ", "ensuring consistency and cross-platform responsiveness.", "Translate UI/UX wireframes and designs into high-quality,", "Collaborate with designers, other developers,", "and other team members to integrate elements smoothly.", "Implement best practices for web accessibility.", "cross-browser compatibility, and user experience."], "salary": "₦1,200,000 - ₦1,500,000", "slots": 2, "category": "Finance", "categoryLink": "ict", "tools": ["React"]}, {"id": "3", "company": "Bloopers", "logo": "/assets/images/loop-studios.svg", "new": true, "featured": true, "position": "React Native Frontend Developer", "role": "Frontend", "level": "Senior", "postedAt": "1d ago", "contract": "Full Time", "location": "Hong Kong", "languages": ["HTML & CSS", "React & React Native", "Typescript/JavaScript"], "description": "We are seeking a talented Front-End Developer to join our development team. This role involves translating UI/UX designs into functional code, optimizing web applications for maximum speed and scalability, and ensuring our products deliver an exceptional user experience. The ideal candidate is skilled in front-end web technologies, has a keen eye for design, and is passionate about delivering high-quality digital products.", "requirements": ["Develop new features for users ", "ensuring consistency and cross-platform responsiveness.", "Translate UI/UX wireframes and designs into high-quality,", "Collaborate with designers, other developers,", "and other team members to integrate elements smoothly.", "Implement best practices for web accessibility.", "cross-browser compatibility, and user experience."], "salary": "$50,000 - $70,000", "slots": 2, "category": "Finance", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "4", "company": "Eyecam Co.", "logo": "/assets/images/eyecam-co.svg", "new": false, "featured": false, "position": "Social Worker", "role": "Social Worker", "level": "Junior", "postedAt": "5d ago", "contract": "Contract", "location": "USA", "languages": ["CSS", "JavaScript"], "description": "We are seeking a talented Social worker to join our development team. This role involves translating UI/UX designs into functional code, optimizing web applications for maximum speed and scalability, and ensuring our products deliver an exceptional user experience. The ideal candidate is skilled in front-end web technologies, has a keen eye for design, and is passionate about delivering high-quality digital products.", "requirements": ["Develop new features for users ", "ensuring consistency and cross-platform responsiveness.", "Translate UI/UX wireframes and designs into high-quality,", "Collaborate with designers, other developers,", "and other team members to integrate elements smoothly.", "Implement best practices for web accessibility.", "cross-browser compatibility, and user experience."], "salary": "$40,000 - $50,000", "slots": 2, "category": "Social Science", "categoryLink": "ict", "tools": []}, {"id": "5", "company": "Loop Studios", "logo": "/assets/images/loop-studios.svg", "new": false, "featured": false, "position": "Software Engineer", "role": "Fullstack", "level": "Mid Level", "postedAt": "1w ago", "contract": "Full Time", "location": "Nigeria", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "₦400,000 - ₦500,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["<PERSON>", "Sass"]}, {"id": "6", "company": "FaceIt", "logo": "/assets/images/faceit.svg", "new": false, "featured": false, "position": "Junior Backend <PERSON>", "role": "Backend", "level": "Junior", "postedAt": "2w ago", "contract": "Full Time", "location": "Nigeria", "languages": ["<PERSON>"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "₦110,000 - ₦140,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["RoR"]}, {"id": "7", "company": "Short Hop", "logo": "/assets/images/shortly.svg", "new": false, "featured": false, "position": "Junior Developer", "role": "Frontend", "level": "Junior", "postedAt": "2w ago", "contract": "Full Time", "location": "Canada", "languages": ["HTML", "JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$40,000 - $50,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["Sass"]}, {"id": "8", "company": "Insure", "logo": "/assets/images/insure.svg", "new": false, "featured": false, "position": "Junior Frontend Developer", "role": "Frontend", "level": "Junior", "postedAt": "2w ago", "contract": "Full Time", "location": "USA", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$40,000 - $50,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["<PERSON><PERSON>", "Sass"]}, {"id": "9", "company": "Eyecam Co.", "logo": "/assets/images/eyecam-co.svg", "new": false, "featured": false, "position": "Full Stack Engineer", "role": "Fullstack", "level": "Mid Level", "postedAt": "3w ago", "contract": "Full Time", "location": "Canada", "languages": ["JavaScript", "Python"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$40,000 - $50,000", "slots": 2, "category": "Finance", "categoryLink": "ict", "tools": ["Django"]}, {"id": "10", "company": " Face Lift Company", "logo": "/assets/images/faceit.svg", "new": false, "featured": false, "position": "Front-end Dev", "role": "Frontend", "level": "Junior", "postedAt": "1mo ago", "contract": "Part Time", "location": "Luxembourg", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$20,000 - $30,000", "slots": 2, "category": "Business", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "11", "company": " Face Lift Company", "logo": "/assets/images/faceit.svg", "new": false, "featured": true, "position": "Graphic Designer", "role": "UI Designer", "level": "Junior", "postedAt": "1mo ago", "contract": "Part Time", "location": "Los Angeles, CA", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$35,000 - $40,000", "slots": 2, "category": "Education", "categoryLink": "ict", "tools": ["Adobe Illustrator", "Adobe photoshop"]}, {"id": "12", "company": " Face Lift Company", "logo": "/assets/images/faceit.svg", "new": false, "featured": true, "position": "Product Manager", "role": "Manager", "level": "Senior", "postedAt": "1mo ago", "contract": "Part Time", "location": "New York, NY ", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$30,000 - $40,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["Adobe Illustrator", "Adobe photoshop"]}, {"id": "13", "company": " Face Lift Company", "logo": "/assets/images/faceit.svg", "new": false, "featured": true, "position": "DevOps enginner", "role": "Software Engineer", "level": "Senior", "postedAt": "1mo ago", "contract": "Part Time", "location": "New York, NY ", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$60,000 - $70,000", "slots": 2, "category": "ICT", "categoryLink": "ict", "tools": ["Adobe Illustrator", "Adobe photoshop"]}, {"id": "14", "company": "GYS inc.", "logo": "/assets/images/myhome.svg", "new": false, "featured": false, "position": "Business Consultant", "role": "Consultant", "level": "Senior", "postedAt": "1mo ago", "contract": "Full Time", "location": "Ethiopia", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$20,000 - $25,000", "slots": 2, "category": "Business", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "15", "company": "GYS inc.", "logo": "/assets/images/myhome.svg", "new": false, "featured": false, "position": "Electrical Engineer", "role": "Electrician", "level": "Senior", "postedAt": "1mo ago", "contract": "Full Time", "location": "Remote", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["<PERSON><PERSON>, ipsum dolor sit amet ", "consectetur adipisicing elit. Error fuga", "a debitis tempore sunt dignissimos, aspernatur libero", "ducimus repellendus aliquam facere doloremque quidem", "sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$15,000 - $20,000", "slots": 2, "category": "Education", "categoryLink": "ict", "tools": ["React", "Sass"]}, {"id": "16", "company": "EastSide", "logo": "/assets/images/faceit.svg", "new": false, "featured": false, "position": "Electrical Engineer", "role": "Electrician", "level": "Senior", "postedAt": "1mo ago", "contract": "Full Time", "location": "Ethiopia", "languages": ["JavaScript"], "description": "<PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit. <PERSON><PERSON>r fuga, a debitis tempore sunt dignissimos, aspernatur libero ducimus repellendus aliquam facere doloremque quidem sit soluta nemo, minima nobis architecto quasi ut doloribus laboriosam animi. Eum suscipit neque sint tempora tenetur.", "requirements": ["sit soluta nemo, minima nobis architecto", "quasi ut doloribus laboriosam animi. Eum suscipit", "neque sint tempora tenetur."], "salary": "$70,000 - $80,000", "slots": 2, "category": "Engineering", "categoryLink": "ict", "tools": ["React", "Sass"]}], "sectors": [{"title": "ICT", "icon": "/assets/images/ict.svg", "link": "ict", "count": 2, "id": "863c"}, {"title": "Business", "icon": "/assets/images/business.svg", "link": "business", "count": 12, "id": "7af6"}, {"title": "Education", "icon": "/assets/images/education.svg", "link": "education", "count": 32, "id": "8719"}, {"title": "Engineering", "icon": "/assets/images/enginnering.svg", "link": "engineering", "count": 22, "id": "55e9"}, {"title": "Finance", "icon": "/assets/images/finance.svg", "link": "finance", "count": 98, "id": "6b6d"}, {"title": "Science", "icon": "/assets/images/science.svg", "link": "science", "count": 3, "id": "bebd"}, {"title": "Social Science", "icon": "/assets/images/social-science.svg", "link": "social-science", "count": 48, "id": "87ce"}], "users": [{"id": "7b90", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "password": "$2b$10$HSUszP3yDntaLF6V9ds1muEd7KejnxsGWSv8rPZEg3TOP82pTNodS"}, {"id": "335f", "firstName": "<PERSON><PERSON>", "lastName": "Princess", "email": "<EMAIL>", "password": "$2b$10$m25qEcp0zslipbi663WKMejKPZRJ2od/1/MQb/cSK8AMyywW3ANBu"}, {"id": "0d85", "firstName": "Adebola", "lastName": "Olatunbosun", "email": "a.o<PERSON>un<PERSON><PERSON>@gmail.com", "password": "$2b$10$4YoWdzqMEP6KEB2CPis.qeBmOEy8NJyCsMxaeGPPZi.HMKqdu2whC"}, {"id": "93e4", "firstName": "Efe", "lastName": "<PERSON>", "email": "<EMAIL>", "password": "$2b$10$qaRvT9G80p//WVnlyx/Q9u/n94ZaoDcV.mlwUjXNxIH/823FDOlzG"}], "applications": [{"id": "1", "jobId": "1", "firstName": "Meekness", "lastName": "<PERSON>", "email": "<EMAIL>", "coverLetter": "I am excited to apply!", "resume": "resume.pdf"}, {"id": "a20b", "jobId": "1", "firstName": "<PERSON>", "lastName": "<PERSON>ori<PERSON><PERSON><PERSON><PERSON>", "email": "kimberly<PERSON>@gmail.com", "coverLetter": "nnnn", "resume": "Green and Cream Modern Beauty Service Price List.pdf"}, {"id": "4f42", "jobId": "1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "coverLetter": "l,l,.,", "resume": "Green and Cream Modern Beauty Service Price List.pdf"}, {"id": "a98f", "jobId": "2", "firstName": "Osemwengie", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "coverLetter": "iyguhopjihgghn ", "resume": "Green and Cream Modern Beauty Service Price List.pdf"}]}