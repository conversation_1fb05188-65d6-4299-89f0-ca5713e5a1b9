.about_container {
    max-width: 800px;
    margin: 0 auto;
    padding: 3rem;
    text-align: center;
  }
  
  .heading {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }
  
  .subtext {
    font-size: 1rem;
    color: #555;
    margin-bottom: 30px;
  }
  
  .section {
    margin-bottom: 30px;
    padding: 20px;
    background: #6eb9ff;
    border-radius: 8px;
  }
  
  .section h3 {
    text-align: left;
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #222;
  }
  
  .section p {
    text-align: left;
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
  }
  
  .section ul {
    list-style: none;
    padding: 0;
  }
  
  .section li {
    text-align: justify;
    font-size: 1rem;
    color: #444;
    margin: 8px 0;
  }
  
  .join_us {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
    margin-top: 20px;
  }
  .page_not_found {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    background-color: #2c2c2c;
  }
  .page_not_found h1 {
    color: #e7195a;
    letter-spacing: 5px;
    font-size: 4rem;
    text-transform: uppercase;
    display: flex;
    align-items: center;
  }
  .page_not_found a {
    color: rgb(107, 179, 0);
    font-weight: 800;
    letter-spacing: 2px;
    text-decoration: underline;
  }
  
  @media only screen and (max-width: 1000px) {
    .about_container {
      padding: 3rem 1rem;
    }
    .section {
      padding: 20px 10px;
    }
    .section h3 {
      text-align: center;
    }
    .section p {
      text-align: justify;
    }
    .page_not_found h1 {
      
      font-size: 2rem;
      letter-spacing: 2px;
      
    }
  }