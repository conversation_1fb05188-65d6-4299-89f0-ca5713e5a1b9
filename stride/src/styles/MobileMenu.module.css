.mobile_menu {
    position: fixed;
    top: 0;
    left: -100%;
    width: 250px;
    height: 100%;
    background: #6eb9ff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease-in-out;
    z-index: 1000;
    padding: 1.5rem;
    visibility: hidden;
  }
  
  .close_btn {
    background: none;
    border: none;
    align-self: flex-end;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 0.5rem;
    width: 40px;
    height: 40px;
  }
  .close_btn svg {
    width: 26px;
    height: 26px;
    color: #000000;
    fill: #000000;
  }
  .mobile_menu_content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .mobile_menu_ftr {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    color: #333;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.3rem;
  }
  .mobile_nav_list,
  .mobile_nav_list_auth {
    display: flex;
    flex-direction: column;
  }
  .mobile_nav_list li {
    margin: 0.5rem 0;
    width: 100%;
  }
  .mobile_nav_list a {
    text-decoration: none;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
  }
  .mob_nav_sign_in,
  .mob_nav_sign_up {
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.3rem 1rem;
    border-radius: 5px;
    display: block;
    margin: 0.5rem 0;
    text-align: center;
  }
  .mob_nav_sign_in {
    border: 1px dashed rgb(2, 0, 128);
    background-color: #6eb9ff;
    color: #2c3e50;
  }
  .mob_nav_sign_up {
    color: #6eb9ff;
    border: 1px  #000000;
    background-color: #2c3e50;
  }
  .mobile_nav_logout_btn{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    color: #fff;
    background-color: #ae0000;
    border-radius: 5px;
    border: 1px solid #333;
    padding: 0.3rem 1rem;
    margin: 1.5rem 0;
    font-size: 0.9rem;
    font-weight: 600;
  }
  @media only screen and (max-width: 770px) {
    .mobile_menu {
      visibility: visible;
    }
  
    .mobile_menu.open {
      left: 0;
    }
  
    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0);
      z-index: -1;
      visibility: hidden;
      transition: background 0.3s ease-in-out, visibility 0s linear 0.3s;
    }
  
    .overlay.show {
      background: rgba(0, 0, 0, 0.6);
      z-index: 999;
      visibility: visible;
      transition: background 0.3s ease-in-out, visibility 0s linear;
    }
  }