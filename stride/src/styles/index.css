@import url('https://fonts.googleapis.com/css2?family=Parkinsans:wght@300..800&display=swap');

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html {
  scroll-behavior: smooth;
  font-size: clamp(14px, 2vw, 16px);
}
html,
body {
  font-family: 'Parkinsans', sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  background-color: #ffffff;
}
#root {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
body {
  word-wrap: break-word;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  position: relative;
  width: 100%;
  min-height: 100vh;
  height: 100%;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

img,
picture,
video,
svg {
  display: block;
  max-width: 100%;
}

button {
  all: unset;
  cursor: pointer;
}