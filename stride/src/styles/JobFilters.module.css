.filters {
    position: sticky;
    width: 100%;
    top: 1rem;
    z-index: 1;
    border-top: 2px solid #ddd;
    border-bottom: 2px solid #ddd;
    background-color: #ffffff;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
  }
  .filters_wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.5rem 3rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
  }
  .search_filter {
    flex: 1 1 400px;
    min-width: 300px;
  }
  
  .category_filter,
  .location_filter,
  .experience_filter {
    position: relative;
    height: 34px;
  }
  
  .category_filter {
    flex: 1 1 200px;
    min-width: 200px;
  }
  .location_filter {
    flex: 1 1 150px;
    min-width: 180px;
    max-width: 50%;
  }
  .experience_filter {
    flex: 1 1 150px;
    min-width: 150px;
    max-width: 50%;
  }
  
  .dropdownButton {
    padding: 0 1rem 0 1.5rem;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    color: white;
    border: 1px solid #004386;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.1rem;
    border-radius: 100vw;
    background-color: #004386;
  }
  .dropdownButton:hover {
    background-color: #33333385;
  }
  .dropdownButton.selected {
    background-color: #0073e6;
    border: 1px solid #333;
  }
  .btn_txt {
    font-size: 0.9rem;
    font-weight: 600;
    line-height: 1;
    text-wrap: pretty;
    width: fit-content;
  }
  .dropdownButton svg {
    width: 16px;
    height: 16px;
  }
  .filter_clear {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .filter_clear svg {
    width: 22px;
    height: 22px;
    fill: rgb(255, 42, 42);
  }
  @media only screen and (max-width: 1200px) {
    .filters_wrapper {
      padding: 0.5rem 2rem;
    }
  }
  
  @media only screen and (max-width: 1000px) {
    .filters_wrapper {
      padding: 0.5rem 1rem;
    }
  }
  
  @media only screen and (max-width: 770px) {
    .category_filter,
    .location_filter,
    .experience_filter {
      position: relative;
      height: 30px;
    }
    .location_filter,
    .experience_filter {
      flex: 1 1 150px;
      min-width: 150px;
    }
    .btn_txt {
      font-size: 0.8rem;
    }
    .filters {
      top: 0;
    }
    .filters_wrapper {
      gap: 0.25rem;
    }
  }
  
  @media only screen and (max-width: 470px) {
    .category_filter,
    .location_filter,
    .experience_filter {
      height: 28px;
    }
    .location_filter,
    .experience_filter {
      max-width: 100%;
    }
    .search_filter{
      min-width: 100%;
    }
  }