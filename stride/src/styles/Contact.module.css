.contact_container {
    max-width: 800px;
    margin: 0 auto;
    padding: 3rem;
    text-align: center;
  }
  
  .heading {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
  }
  
  .subtext {
    font-size: 1rem;
    color: #555;
    margin-bottom: 2rem;
  }
  
  .contact_info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    text-align: left;
    margin-bottom: 2rem;
  }
  
  .contact_info div {
    width: 100%;
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 8px;
  }
  
  .contact_info h3 {
    margin-bottom: 5px;
    color: #222;
  }
  
  .contact_info p {
    font-size: 0.9rem;
    color: #666;
  }
  
  .subheading {
    font-size: 1.5rem;
    margin: 20px 0;
  }
  
  .contact_form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .contact_form input,
  .contact_form textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
  }
  .contact_form textarea {
    resize: vertical;
    max-height: 100px;
    min-height: 50px;
  }
  .contact_form input:focus,
  .contact_form textarea:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
  .contact_form button {
    background: #007bff;
    color: white;
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease-in-out;
  }
  
  .contact_form button:hover {
    background: #0056b3;
  }
  @media only screen and (max-width: 1000px) {
    .contact_container {
      padding: 3rem 1rem;
    }
  }