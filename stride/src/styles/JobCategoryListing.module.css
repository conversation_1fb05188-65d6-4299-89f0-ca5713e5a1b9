.job_category_list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    padding: 0.5rem 0;
    margin: 0 auto 3rem;
  }
  .category_card_wrap {
    width: 100%;
    padding: 1.5rem;
    box-shadow: 1px 3px 5px #333;
    border-radius: 5px;
    background-color: #fff;
  }
  .category_card {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
  }
  .category_card h3 {
    letter-spacing: 1px;
    margin-bottom: 1rem;
    color: #2c3e50;
  }
  .category_card img {
    width: 70px;
    height: 70px;
    object-fit: contain;
  }
  .category_card p {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 500;
    margin-top: 1.5rem;
  }
  /* ON FILTER MODE */
  
  .dropdown {
    position: absolute;
    top: 105%;
    left: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
    max-height: 200px;
    width: 100%;
    overflow-y: auto;
    z-index: 10;
  }
  .dropdownMenu {
    display: flex;
    flex-direction: column;
  }
  
  .dropdownItem {
    padding: 0.5rem 0.5rem 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 1px solid #ddd;
  }
  .dropdownItem img {
    width: 30px;
    height: 30px;
  }
  .dropdownItem:hover {
    background-color: #ffd700;
  }
  
  @media only screen and (max-width: 770px) {
    .job_category_list {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 0.5rem;
    }
    .category_card_wrap {
      padding: 1rem 0.5rem;
      box-shadow: 1px 3px 5px #33333355;
    }
    
    .category_card h3 {
      font-size: 1rem;
      letter-spacing: 1px;
      margin-bottom: 0.5rem;
    }
    .category_card img {
      width: 50px;
      height: 50px;
    }
    .category_card p {
      font-size: 0.9rem;
      margin-top: 1rem;
    }
  }