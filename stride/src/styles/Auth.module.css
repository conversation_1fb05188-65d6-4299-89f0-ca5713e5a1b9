.auth_wrapper {
    width: 100%;
    background-color: rgb(129, 230, 248);
    min-height: calc(100vh - 70px);
    background-image: url(/assets/images/job-hero.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }
  .auth_container {
    width: 100%;
    height: 100%;
    padding: 2rem 1rem 1rem;
    position: relative;
  }
  .back_link {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    border: 2px solid #ffffff;
    border-radius: 100vw;
    display: flex;
    align-items: center;
    gap: 0.2rem;
    color: black;
    background-color: rgb(129, 230, 248);
  }
  .back_link:hover {
    background-color: #ffffff;
    color: #333;
  }
  .back_link svg {
    width: 22px;
    height: 22px;
  }
  .auth_form_container {
    background: #75757566;
    backdrop-filter: blur(3px);
    border-radius: 10px;
    padding: 2rem 2rem;
    box-shadow: 0 0px 10px #0057b357;
    width: 100%;
    height: max-content;
    max-width: 400px;
    margin: auto;
    color: #fff;
    border: 3px #00366b;
    position: relative;
  }
  .auth_loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
  }
  .auth_form_container h2 {
    font-size: 1.8rem;
    letter-spacing: 1px;
    color: #ffffff;
    text-align: center;
  }
  .form_input_row {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
  }
  .form_input_group,
  .form_input_group_chkbox {
    margin-top: 0.7rem;
    position: relative;
  }
  .form_input_group label {
    display: block;
    text-align: left;
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 1;
    margin-left: 5px;
    margin-bottom: 3px;
    color: #ffffff;
  }
  
  .form_input_group input {
    width: 100%;
    padding: 0.4rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
  }
  .form_input_group input:focus {
    outline: 2px solid blue;
    outline-offset: 2px;
  }
  .password_toggle {
    position: absolute;
    right: 0.5rem;
    bottom: 5px;
    cursor: pointer;
    color: #7c7f83;
  }
  .password_toggle svg {
    width: 20px;
    height: 20px;
  }
  .password_toggle:hover {
    color: #333;
  }
  .chkbox_label {
    display: flex;
    align-items: flex-end;
    gap: 5px;
  }
  .chkbox_label span {
    line-height: 1;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    color: #fff;
  }
  .chkbox_label span a {
    font-weight: 600;
    color: #001a28;
    text-decoration: underline;
  }
  .chkbox_label span a:hover {
    color: #333;
  }
  .form_btn {
    width: 100%;
    padding: 0.5rem;
    background-color: #6eb9ff;
    color: white;
    border: none;
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 1rem;
    box-sizing: border-box;
  }
  
  .form_btn:hover {
    background-color: #0056b3;
  }
  .form_redirect_link {
    margin-top: 0.5rem;
    text-align: right;
    font-size: 0.8rem;
    font-weight: 500;
    color: #fff;
  }
  .form_redirect_link a {
    color: #001a28;
    text-decoration: underline;
  }
  .form_redirect_link a:hover {
    color: #333;
  }
  
  
  @media only screen and (max-width: 770px) {
    .auth_wrapper {
      background-position: bottom;
    }
    .back_link {
      left: 0;
    }
    .auth_container {
      padding: 5rem 0 2rem;
    }
    .auth_form_container {
      padding: 2rem 1.5rem;
    }
  }