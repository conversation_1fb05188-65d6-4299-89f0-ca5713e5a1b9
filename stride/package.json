{"name": "stride", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "npx tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.12", "axios": "^1.8.2", "bcryptjs": "^3.0.2", "dompurify": "^3.2.4", "json": "^11.0.0", "json-server": "^1.0.0-beta.3", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.3.0", "react-spinners": "^0.15.0", "server": "^0.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "install": "^0.13.0", "postcss": "^8.5.3", "tailwindcss": "^4.0.12", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "typescriptnpm": "^1.0.1", "vite": "^6.2.0"}}